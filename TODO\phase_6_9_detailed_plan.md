# Vue前端开发 Phase 6-9 详细实施计划

## 📋 项目上下文回顾

### 🎯 技术栈基础
- **前端框架**: Vue 3 + TypeScript + Vite
- **状态管理**: Pinia (已完成验证 ✅)
- **样式方案**: Tailwind CSS
- **组合式函数**: 9个核心composables已完成 ✅
- **API集成**: 完整的服务层已建立 ✅

### 🔗 已验证的核心能力
- ✅ 认证流程 (useAuth)
- ✅ 流式聊天 (useChat + useWebSocket)
- ✅ 模型管理 (useModels)
- ✅ 主题切换 (useTheme)
- ✅ 权限控制 (usePermission)
- ✅ 表单验证 (useValidation)

---

## 📅 第3周：组件开发周 (Phase 6-7)

## **Phase 6: 基础组件库** 🧱
**时间安排**: 第3周 第1-3天 (周一至周三)
**目标**: 构建类型安全、可复用的基础组件系统

### 📁 详细文件结构
```
src/components/base/
├── Button/
│   ├── index.vue           # 主按钮组件
│   ├── ButtonGroup.vue     # 按钮组
│   ├── types.ts           # 按钮类型定义
│   └── styles.ts          # 样式配置
├── Input/
│   ├── index.vue           # 基础输入框
│   ├── TextArea.vue        # 文本域
│   ├── InputGroup.vue      # 输入组
│   ├── types.ts
│   └── composables.ts      # 输入框专用逻辑
├── Modal/
│   ├── index.vue           # 模态框
│   ├── ConfirmDialog.vue   # 确认对话框
│   ├── AlertDialog.vue     # 警告对话框
│   └── types.ts
├── Loading/
│   ├── index.vue           # 基础加载器
│   ├── Skeleton.vue        # 骨架屏
│   ├── ProgressBar.vue     # 进度条
│   └── SpinnerVariants.vue # 多种加载动画
├── Card/
│   ├── index.vue           # 基础卡片
│   ├── CardHeader.vue      # 卡片头部
│   ├── CardBody.vue        # 卡片内容
│   ├── CardFooter.vue      # 卡片底部
│   └── types.ts
├── Avatar/
│   ├── index.vue           # 头像组件
│   ├── AvatarGroup.vue     # 头像组
│   └── types.ts
├── Form/
│   ├── index.vue           # 表单容器
│   ├── FormItem.vue        # 表单项
│   ├── FormLabel.vue       # 表单标签
│   ├── FormError.vue       # 错误提示
│   └── useForm.ts          # 与useValidation集成
├── Table/
│   ├── index.vue           # 基础表格
│   ├── TableHeader.vue     # 表头
│   ├── TableRow.vue        # 表格行
│   ├── TableCell.vue       # 表格单元格
│   └── types.ts
└── index.ts                # 组件库统一导出
```

### 🚀 Phase 6 实施步骤

#### **Day 1: 核心交互组件**
**上午 (4小时):**
1. **项目架构搭建**
   ```bash
   # 创建组件库基础结构
   mkdir -p src/components/base/{Button,Input,Modal,Loading,Card,Avatar,Form,Table}
   
   # 创建组件测试页面
   touch src/views/dev/ComponentShowcase.vue
   ```

2. **Button组件开发** (基于已有useTheme)
   ```typescript
   // Button/types.ts
   export interface ButtonProps {
     variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger'
     size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
     loading?: boolean
     disabled?: boolean
     icon?: string
     iconPosition?: 'left' | 'right'
     block?: boolean
   }
   ```

3. **Input组件开发** (集成useValidation)
   ```typescript
   // Input/types.ts
   export interface InputProps {
     modelValue?: string | number
     type?: 'text' | 'password' | 'email' | 'tel' | 'url'
     placeholder?: string
     disabled?: boolean
     readonly?: boolean
     error?: string | boolean
     success?: boolean
     clearable?: boolean
     showPassword?: boolean // for password type
   }
   ```

**下午 (4小时):**
4. **Modal组件开发**
   - 集成已有的useNotification
   - 支持确认对话框、警告对话框
   - 键盘导航和无障碍支持

5. **组件测试页面**
   - 创建 `/dev/components` 路由
   - 实时预览所有组件变体
   - 测试与composables的集成

#### **Day 2: 数据展示组件**
**上午 (4小时):**
1. **Card组件开发**
   - 支持头部、内容、底部插槽
   - 可配置阴影、边框、圆角
   - 响应式布局支持

2. **Avatar组件开发**
   - 支持图片、字母、图标头像
   - 状态指示器集成
   - 尺寸变体完整

**下午 (4小时):**
3. **Loading组件开发**
   - 多种加载动画
   - 骨架屏组件
   - 进度条组件
   - 与异步状态集成

4. **Table组件开发** (简化版)
   - 基础表格布局
   - 排序功能
   - 选择功能
   - 响应式处理

#### **Day 3: 表单系统和集成**
**上午 (4小时):**
1. **Form组件开发**
   ```typescript
   // Form/useForm.ts - 基于已有useValidation
   export function useForm<T>(schema: ValidationSchema<T>) {
     const { validate, errors, isValid } = useValidation(schema)
     
     // 表单提交逻辑
     const handleSubmit = async (data: T) => {
       if (await validate(data)) {
         return data // 验证通过
       }
       throw new Error('表单验证失败')
     }
     
     return { handleSubmit, errors, isValid }
   }
   ```

2. **组件库文档**
   - 每个组件的使用示例
   - API文档生成
   - 最佳实践指南

**下午 (4小时):**
3. **组件库集成测试**
   - 所有组件的功能测试
   - 响应式测试
   - 无障碍测试
   - 性能测试

4. **组件库打包发布**
   ```typescript
   // components/base/index.ts
   export { default as BaseButton } from './Button/index.vue'
   export { default as BaseInput } from './Input/index.vue'
   export { default as BaseModal } from './Modal/index.vue'
   // ... 所有组件导出
   
   export type * from './Button/types'
   export type * from './Input/types'
   // ... 所有类型导出
   ```

### ✅ Phase 6 验收标准
- [ ] 8个基础组件全部实现且类型安全
- [ ] 所有组件支持暗色/亮色主题 (基于useTheme)
- [ ] 组件库文档完整，包含使用示例
- [ ] 响应式设计在所有设备上正常工作
- [ ] 与已有composables完美集成
- [ ] 组件测试页面功能完整

---

## **Phase 7: 业务组件开发** 🏢
**时间安排**: 第3周 第4-5天 (周四至周五)
**目标**: 基于基础组件构建业务特定组件

### 📁 详细文件结构
```
src/components/business/
├── ChatMessage/
│   ├── index.vue              # 消息容器组件
│   ├── MessageBubble.vue      # 消息气泡
│   ├── MessageActions.vue     # 消息操作按钮
│   ├── MessageTime.vue        # 时间显示
│   ├── MessageStatus.vue      # 消息状态
│   ├── ThinkingIndicator.vue  # AI思考指示器
│   ├── StreamingText.vue      # 流式文本显示
│   └── types.ts
├── ModelSelector/
│   ├── index.vue              # 模型选择器主组件
│   ├── ModelCard.vue          # 模型卡片
│   ├── ModelInfo.vue          # 模型详情
│   ├── ModelTest.vue          # 模型测试
│   └── types.ts
├── ConversationList/
│   ├── index.vue              # 对话列表
│   ├── ConversationItem.vue   # 对话项
│   ├── ConversationSearch.vue # 对话搜索
│   └── types.ts
├── UserProfile/
│   ├── index.vue              # 用户资料显示
│   ├── UserAvatar.vue         # 用户头像
│   ├── UserStatus.vue         # 用户状态
│   └── types.ts
├── BillingChart/
│   ├── index.vue              # 计费图表
│   ├── UsageChart.vue         # 使用量图表
│   ├── CostChart.vue          # 成本图表
│   └── types.ts
├── MarkdownRenderer/
│   ├── index.vue              # Markdown渲染器
│   ├── CodeBlock.vue          # 代码块组件
│   ├── MathBlock.vue          # 数学公式组件
│   └── types.ts
├── FeatureToggle/
│   ├── index.vue              # 功能开关组件
│   ├── RagToggle.vue          # RAG开关
│   ├── ThemeToggle.vue        # 主题切换
│   └── types.ts
└── index.ts                   # 业务组件统一导出
```

### 🚀 Phase 7 实施步骤

#### **Day 4: 聊天核心组件**
**上午 (4小时):**
1. **ChatMessage组件开发**
   ```vue
   <!-- ChatMessage/index.vue -->
   <template>
     <div class="chat-message" :class="messageClass">
       <UserAvatar v-if="showAvatar" :user="message.user" />
       <div class="message-content">
         <MessageBubble 
           :content="message.content"
           :type="message.type"
           :streaming="message.streaming"
         />
         <ThinkingIndicator v-if="message.thinking" />
         <MessageActions 
           :message="message"
           @copy="handleCopy"
           @retry="handleRetry"
         />
         <MessageTime :time="message.createdAt" />
       </div>
     </div>
   </template>
   
   <script setup lang="ts">
   // 集成 useChat composable
   import { useChat } from '@/composables'
   
   const { retryMessage, copyMessage } = useChat()
   </script>
   ```

2. **StreamingText组件**
   ```vue
   <!-- 支持流式文本显示和thinking内容 -->
   <template>
     <div class="streaming-text">
       <div v-if="showThinking" class="thinking-content">
         <i class="fas fa-brain animate-pulse"></i>
         <span>{{ thinkingText }}</span>
       </div>
       <div class="response-content">
         <MarkdownRenderer :content="displayText" />
       </div>
     </div>
   </template>
   ```

**下午 (4小时):**
3. **ModelSelector组件开发**
   ```vue
   <!-- ModelSelector/index.vue -->
   <template>
     <div class="model-selector">
       <BaseCard>
         <template #header>
           <div class="model-header">
             <h3>选择AI模型</h3>
             <BaseButton 
               variant="ghost" 
               size="sm"
               @click="refreshModels"
             >
               <i class="fas fa-refresh"></i>
             </BaseButton>
           </div>
         </template>
         
         <div class="model-grid">
           <ModelCard
             v-for="model in availableModels"
             :key="model.id"
             :model="model"
             :selected="isSelectedModel(model.id)"
             @select="selectModel"
           />
         </div>
       </BaseCard>
     </div>
   </template>
   
   <script setup lang="ts">
   // 集成 useModels composable
   import { useModels } from '@/composables'
   
   const { 
     availableModels, 
     currentModel, 
     selectModel,
     refreshModels,
     isModelSelected 
   } = useModels()
   </script>
   ```

4. **ConversationList组件开发**
   - 集成useChat的对话管理功能
   - 支持搜索、筛选、排序
   - 虚拟滚动优化性能

#### **Day 5: 辅助和展示组件**
**上午 (4小时):**
1. **MarkdownRenderer组件**
   ```bash
   # 安装必要依赖
   npm install marked highlight.js katex
   ```
   
   ```vue
   <!-- MarkdownRenderer/index.vue -->
   <template>
     <div class="markdown-content" v-html="renderedContent"></div>
   </template>
   
   <script setup lang="ts">
   import { marked } from 'marked'
   import hljs from 'highlight.js'
   import katex from 'katex'
   
   // 自定义渲染器配置
   const renderer = new marked.Renderer()
   </script>
   ```

2. **BillingChart组件开发**
   ```bash
   # 安装图表库
   npm install chart.js vue-chartjs
   ```
   
   ```vue
   <!-- 集成 useBilling composable -->
   <script setup lang="ts">
   import { useBilling } from '@/composables'
   
   const { usageStats, billingInfo, refreshStats } = useBilling()
   </script>
   ```

**下午 (4小时):**
3. **FeatureToggle组件开发**
   ```vue
   <!-- FeatureToggle/ThemeToggle.vue -->
   <template>
     <div class="theme-toggle" @click="toggleTheme">
       <div class="toggle-track" :class="{ active: isDark }">
         <div class="toggle-thumb">
           <i :class="themeIcon"></i>
         </div>
       </div>
     </div>
   </template>
   
   <script setup lang="ts">
   // 集成 useTheme composable
   import { useTheme } from '@/composables'
   
   const { isDark, toggleTheme, themeIcon } = useTheme()
   </script>
   ```

4. **业务组件集成测试**
   - 创建业务组件演示页面
   - 测试与composables的集成
   - 性能优化和错误处理

### ✅ Phase 7 验收标准
- [ ] 7个业务组件全部实现且功能完整
- [ ] 聊天组件支持流式消息和thinking显示
- [ ] 模型选择器与useModels完美集成
- [ ] Markdown渲染支持代码高亮和数学公式
- [ ] 所有组件响应式设计良好
- [ ] 与Phase 6基础组件无缝协作

---

## 📅 第4周：页面和路由周 (Phase 8-9)

## **Phase 8: 页面视图开发** 📄
**时间安排**: 第4周 第1-3天 (周一至周三)
**目标**: 构建完整的页面视图系统

### 📁 详细文件结构
```
src/views/
├── auth/
│   ├── LoginView.vue          # ✅ 已完成
│   ├── RegisterView.vue       # 用户注册页面
│   ├── ForgotPasswordView.vue # 忘记密码
│   └── ResetPasswordView.vue  # 重置密码
├── chat/
│   ├── ChatView.vue           # 🎯 主聊天界面
│   ├── ConversationView.vue   # 对话详情页面
│   └── components/
│       ├── ChatSidebar.vue    # 聊天侧边栏
│       ├── MessageInput.vue   # 消息输入区域
│       ├── MessageList.vue    # 消息列表区域
│       ├── ModelPanel.vue     # 模型配置面板
│       └── SettingsPanel.vue  # 聊天设置面板
├── admin/
│   ├── DashboardView.vue      # 🎯 管理仪表板
│   ├── ModelManageView.vue    # 模型管理页面
│   ├── UserManageView.vue     # 用户管理页面
│   ├── BillingView.vue        # 计费管理页面
│   ├── SystemView.vue         # 系统设置页面
│   └── components/
│       ├── AdminSidebar.vue   # 管理侧边栏
│       ├── StatsCards.vue     # 统计卡片
│       ├── ModelTable.vue     # 模型管理表格
│       └── UserTable.vue      # 用户管理表格
├── profile/
│   ├── ProfileView.vue        # 用户资料页面
│   ├── SettingsView.vue       # 个人设置页面
│   ├── BillingProfileView.vue # 个人计费页面
│   └── components/
│       ├── ProfileForm.vue    # 资料表单
│       ├── PasswordForm.vue   # 密码修改
│       └── PreferencesForm.vue # 偏好设置
├── error/
│   ├── 404View.vue           # 404错误页面
│   ├── 500View.vue           # 500错误页面
│   ├── ForbiddenView.vue     # 403权限错误
│   └── MaintenanceView.vue   # 维护页面
├── dev/
│   ├── ComponentShowcase.vue  # ✅ 组件展示 (Phase 6)
│   ├── ComposableTest.vue     # ✅ Composable测试 (Phase 5)
│   └── ApiTest.vue            # API测试页面
└── layout/
    ├── DefaultLayout.vue      # 默认布局
    ├── AuthLayout.vue         # 认证页面布局
    ├── AdminLayout.vue        # 管理后台布局
    └── components/
        ├── AppHeader.vue      # 应用头部
        ├── AppSidebar.vue     # 应用侧边栏
        └── AppFooter.vue      # 应用底部
```

### 🚀 Phase 8 实施步骤

#### **Day 1: 布局系统开发**
**上午 (4小时):**
1. **布局组件开发**
   ```vue
   <!-- layout/DefaultLayout.vue -->
   <template>
     <div class="app-layout" :class="layoutClass">
       <AppHeader />
       <div class="layout-body">
         <AppSidebar v-if="showSidebar" />
         <main class="main-content">
           <router-view />
         </main>
       </div>
       <AppFooter v-if="showFooter" />
     </div>
   </template>
   
   <script setup lang="ts">
   import { useAuth, useTheme } from '@/composables'
   
   const { isAuthenticated } = useAuth()
   const { isDark, currentTheme } = useTheme()
   </script>
   ```

2. **AppHeader组件**
   ```vue
   <!-- layout/components/AppHeader.vue -->
   <template>
     <header class="app-header">
       <div class="header-left">
         <BaseButton variant="ghost" @click="toggleSidebar">
           <i class="fas fa-bars"></i>
         </BaseButton>
         <h1 class="app-title">AI智能对话系统</h1>
       </div>
       
       <div class="header-right">
         <ThemeToggle />
         <UserProfile />
       </div>
     </header>
   </template>
   ```

**下午 (4小时):**
3. **认证页面开发**
   ```vue
   <!-- auth/RegisterView.vue -->
   <template>
     <AuthLayout>
       <BaseCard class="auth-card">
         <template #header>
           <h2>用户注册</h2>
         </template>
         
         <BaseForm @submit="handleRegister">
           <BaseInput
             v-model="form.username"
             label="用户名"
             :error="errors.username"
             required
           />
           <BaseInput
             v-model="form.email"
             type="email"
             label="邮箱"
             :error="errors.email"
             required
           />
           <BaseInput
             v-model="form.password"
             type="password"
             label="密码"
             :error="errors.password"
             required
           />
           
           <BaseButton 
             type="submit" 
             :loading="isRegistering"
             block
           >
             注册
           </BaseButton>
         </BaseForm>
       </BaseCard>
     </AuthLayout>
   </template>
   
   <script setup lang="ts">
   import { useAuth, useValidation } from '@/composables'
   
   const { register, isRegistering } = useAuth()
   const { validate, errors } = useValidation({
     username: ['required', 'minLength:3'],
     email: ['required', 'email'],
     password: ['required', 'minLength:8']
   })
   </script>
   ```

4. **错误页面开发**
   - 404、500、403等错误页面
   - 统一的错误处理逻辑
   - 友好的用户提示

#### **Day 2: 主聊天界面开发** 🎯
**上午 (4小时):**
1. **ChatView主页面**
   ```vue
   <!-- chat/ChatView.vue -->
   <template>
     <DefaultLayout>
       <div class="chat-container">
         <ChatSidebar 
           v-model:show="showSidebar"
           @conversation-select="handleConversationSelect"
         />
         
         <div class="chat-main">
           <div class="chat-header">
             <h2>{{ currentConversationTitle }}</h2>
             <div class="chat-actions">
               <BaseButton @click="clearChat">清空对话</BaseButton>
               <BaseButton @click="showSettings = true">设置</BaseButton>
             </div>
           </div>
           
           <MessageList 
             ref="messageList"
             :messages="messages"
             :loading="isLoading"
           />
           
           <MessageInput 
             @send="handleSendMessage"
             :disabled="!hasSelectedModel"
           />
         </div>
         
         <ModelPanel 
           v-model:show="showModelPanel"
           @model-change="handleModelChange"
         />
       </div>
     </DefaultLayout>
   </template>
   
   <script setup lang="ts">
   import { useChat, useModels, useWebSocket } from '@/composables'
   
   const { 
     messages, 
     sendMessage, 
     currentConversation,
     isLoading 
   } = useChat()
   
   const { currentModel, hasSelectedModel } = useModels()
   const { connectionStatus } = useWebSocket()
   </script>
   ```

2. **MessageList组件**
   ```vue
   <!-- chat/components/MessageList.vue -->
   <template>
     <div class="message-list" ref="messageContainer">
       <RecycleScroller
         v-if="messages.length > 100"
         class="scroller"
         :items="messages"
         :item-size="estimateSize"
         key-field="id"
         v-slot="{ item }"
       >
         <ChatMessage :message="item" />
       </RecycleScroller>
       
       <div v-else class="simple-list">
         <ChatMessage 
           v-for="message in messages"
           :key="message.id"
           :message="message"
         />
       </div>
       
       <div v-if="isLoading" class="loading-indicator">
         <LoadingSpinner />
       </div>
     </div>
   </template>
   ```

**下午 (4小时):**
3. **ChatSidebar组件**
   ```vue
   <!-- chat/components/ChatSidebar.vue -->
   <template>
     <aside class="chat-sidebar" :class="{ show: modelValue }">
       <div class="sidebar-header">
         <h3>对话历史</h3>
         <BaseButton 
           variant="primary" 
           size="sm"
           @click="createNewConversation"
         >
           <i class="fas fa-plus"></i>
           新对话
         </BaseButton>
       </div>
       
       <ConversationSearch 
         v-model="searchQuery"
         @search="handleSearch"
       />
       
       <ConversationList 
         :conversations="filteredConversations"
         :current="currentConversationId"
         @select="handleSelect"
         @delete="handleDelete"
       />
       
       <div class="sidebar-footer">
         <ModelSelector compact />
         <FeatureToggle />
       </div>
     </aside>
   </template>
   
   <script setup lang="ts">
   import { useChat } from '@/composables'
   
   const { 
     conversations,
     currentConversationId,
     createConversation,
     deleteConversation
   } = useChat()
   </script>
   ```

4. **MessageInput组件**
   ```vue
   <!-- chat/components/MessageInput.vue -->
   <template>
     <div class="message-input-container">
       <div class="input-toolbar" v-if="showToolbar">
         <BaseButton size="sm" @click="attachFile">
           <i class="fas fa-paperclip"></i>
         </BaseButton>
         <BaseButton size="sm" @click="toggleEmoji">
           <i class="fas fa-smile"></i>
         </BaseButton>
       </div>
       
       <div class="input-area">
         <BaseTextArea
           v-model="message"
           placeholder="输入消息..."
           :disabled="disabled"
           @keydown="handleKeydown"
           @input="handleInput"
           auto-resize
           :max-rows="10"
         />
         
         <BaseButton 
           variant="primary"
           :disabled="!canSend"
           :loading="isSending"
           @click="handleSend"
         >
           <i class="fas fa-paper-plane"></i>
         </BaseButton>
       </div>
       
       <div class="input-status">
         <span v-if="isTyping">正在输入...</span>
         <span v-if="characterCount > maxLength" class="error">
           字符超出限制 ({{ characterCount }}/{{ maxLength }})
         </span>
       </div>
     </div>
   </template>
   
   <script setup lang="ts">
   import { useChat, useValidation } from '@/composables'
   
   const { sendMessage, isSending } = useChat()
   
   // 输入验证
   const { validate } = useValidation({
     message: ['required', 'maxLength:4000']
   })
   
   // 快捷键处理
   const handleKeydown = (event: KeyboardEvent) => {
     if (event.key === 'Enter' && !event.shiftKey) {
       event.preventDefault()
       handleSend()
     }
   }
   </script>
   ```

#### **Day 3: 管理后台开发** 📊
**上午 (4小时):**
1. **DashboardView仪表板**
   ```vue
   <!-- admin/DashboardView.vue -->
   <template>
     <AdminLayout>
       <div class="dashboard">
         <div class="dashboard-header">
           <h1>系统仪表板</h1>
           <div class="header-actions">
             <BaseButton @click="refreshData">
               <i class="fas fa-refresh"></i>
               刷新数据
             </BaseButton>
           </div>
         </div>
         
         <StatsCards 
           :stats="systemStats"
           :loading="isLoading"
         />
         
         <div class="dashboard-grid">
           <BaseCard title="用户活跃度">
             <UsageChart :data="userActivityData" />
           </BaseCard>
           
           <BaseCard title="模型使用统计">
             <ModelUsageChart :data="modelUsageData" />
           </BaseCard>
           
           <BaseCard title="费用趋势">
             <BillingChart :data="billingTrendData" />
           </BaseCard>
           
           <BaseCard title="系统状态">
             <SystemStatus :status="systemStatus" />
           </BaseCard>
         </div>
       </div>
     </AdminLayout>
   </template>
   
   <script setup lang="ts">
   import { useBilling, usePermission } from '@/composables'
   
   // 权限检查
   const { requirePermission } = usePermission()
   requirePermission('admin.dashboard.view')
   
   // 数据获取
   const { systemStats, refreshStats } = useBilling()
   </script>
   ```

2. **ModelManageView模型管理**
   ```vue
   <!-- admin/ModelManageView.vue -->
   <template>
     <AdminLayout>
       <div class="model-management">
         <div class="page-header">
           <h1>模型管理</h1>
           <BaseButton variant="primary" @click="showCreateModal = true">
             <i class="fas fa-plus"></i>
             添加模型
           </BaseButton>
         </div>
         
         <div class="filters">
           <BaseInput 
             v-model="searchQuery"
             placeholder="搜索模型..."
             clearable
           />
           <BaseSelect 
             v-model="statusFilter"
             placeholder="状态筛选"
             :options="statusOptions"
           />
         </div>
         
         <ModelTable 
           :models="filteredModels"
           :loading="isLoading"
           @edit="handleEdit"
           @delete="handleDelete"
           @test="handleTest"
         />
         
         <!-- 模型创建/编辑弹窗 -->
         <ModelEditModal 
           v-model:show="showCreateModal"
           :model="editingModel"
           @save="handleSave"
         />
       </div>
     </AdminLayout>
   </template>
   
   <script setup lang="ts">
   import { useModels, usePermission } from '@/composables'
   
   const { requirePermission } = usePermission()
   requirePermission('admin.models.manage')
   
   const { 
     models, 
     createModel, 
     updateModel, 
     deleteModel,
     testModel 
   } = useModels()
   </script>
   ```

**下午 (4小时):**
3. **UserManageView用户管理**
   - 用户列表展示
   - 用户权限管理
   - 用户统计信息
   - 批量操作功能

4. **个人资料页面**
   ```vue
   <!-- profile/ProfileView.vue -->
   <template>
     <DefaultLayout>
       <div class="profile-container">
         <div class="profile-sidebar">
           <UserAvatar :user="currentUser" size="lg" />
           <nav class="profile-nav">
             <router-link to="/profile" exact-active-class="active">
               基本信息
             </router-link>
             <router-link to="/profile/settings" active-class="active">
               账户设置
             </router-link>
             <router-link to="/profile/billing" active-class="active">
               计费信息
             </router-link>
           </nav>
         </div>
         
         <div class="profile-content">
           <router-view />
         </div>
       </div>
     </DefaultLayout>
   </template>
   
   <script setup lang="ts">
   import { useAuth } from '@/composables'
   
   const { currentUser } = useAuth()
   </script>
   ```

### ✅ Phase 8 验收标准
- [ ] 所有主要页面完成开发且功能正常
- [ ] 布局系统支持响应式设计
- [ ] 聊天界面用户体验流畅
- [ ] 管理后台功能完整
- [ ] 权限控制正确实施
- [ ] 错误页面和异常处理完善

---

## **Phase 9: 路由系统** 🛣️
**时间安排**: 第4周 第4-5天 (周四至周五)
**目标**: 完善路由配置和权限控制系统

### 📁 详细文件结构
```
src/router/
├── index.ts              # 路由器主配置
├── guards.ts             # 全局路由守卫
├── middleware.ts         # 路由中间件
├── types.ts              # 路由类型定义
└── routes/
    ├── index.ts          # 路由汇总
    ├── auth.ts           # 认证相关路由
    ├── chat.ts           # 聊天相关路由
    ├── admin.ts          # 管理后台路由
    ├── profile.ts        # 用户资料路由
    ├── error.ts          # 错误页面路由
    └── dev.ts            # 开发调试路由
```

### 🚀 Phase 9 实施步骤

#### **Day 4: 路由结构设计**
**上午 (4小时):**
1. **路由类型定义**
   ```typescript
   // router/types.ts
   export interface RouteMetaData {
     title?: string
     requiresAuth?: boolean
     permissions?: string[]
     roles?: string[]
     layout?: 'default' | 'auth' | 'admin'
     breadcrumb?: BreadcrumbItem[]
     cache?: boolean
     transition?: string
   }
   
   export interface BreadcrumbItem {
     title: string
     path?: string
     icon?: string
   }
   ```

2. **认证路由配置**
   ```typescript
   // router/routes/auth.ts
   import type { RouteRecordRaw } from 'vue-router'
   
   export const authRoutes: RouteRecordRaw[] = [
     {
       path: '/auth',
       component: () => import('@/views/layout/AuthLayout.vue'),
       redirect: '/auth/login',
       meta: { 
         requiresAuth: false,
         layout: 'auth' 
       },
       children: [
         {
           path: 'login',
           name: 'Login',
           component: () => import('@/views/auth/LoginView.vue'),
           meta: { 
             title: '用户登录',
             transition: 'slide-left'
           }
         },
         {
           path: 'register',
           name: 'Register',
           component: () => import('@/views/auth/RegisterView.vue'),
           meta: { 
             title: '用户注册',
             transition: 'slide-left'
           }
         },
         {
           path: 'forgot-password',
           name: 'ForgotPassword',
           component: () => import('@/views/auth/ForgotPasswordView.vue'),
           meta: { 
             title: '忘记密码' 
           }
         }
       ]
     }
   ]
   ```

3. **聊天路由配置**
   ```typescript
   // router/routes/chat.ts
   export const chatRoutes: RouteRecordRaw[] = [
     {
       path: '/chat',
       component: () => import('@/views/layout/DefaultLayout.vue'),
       meta: { 
         requiresAuth: true,
         permissions: ['chat.access']
       },
       children: [
         {
           path: '',
           name: 'Chat',
           component: () => import('@/views/chat/ChatView.vue'),
           meta: {
             title: '智能对话',
             breadcrumb: [
               { title: '首页', path: '/' },
               { title: '智能对话' }
             ],
             cache: true
           }
         },
         {
           path: 'conversation/:id',
           name: 'Conversation',
           component: () => import('@/views/chat/ConversationView.vue'),
           meta: {
             title: '对话详情',
             breadcrumb: [
               { title: '首页', path: '/' },
               { title: '智能对话', path: '/chat' },
               { title: '对话详情' }
             ]
           }
         }
       ]
     }
   ]
   ```

**下午 (4小时):**
4. **管理路由配置**
   ```typescript
   // router/routes/admin.ts
   export const adminRoutes: RouteRecordRaw[] = [
     {
       path: '/admin',
       component: () => import('@/views/layout/AdminLayout.vue'),
       meta: { 
         requiresAuth: true,
         permissions: ['admin.access'],
         layout: 'admin'
       },
       children: [
         {
           path: '',
           redirect: '/admin/dashboard'
         },
         {
           path: 'dashboard',
           name: 'AdminDashboard',
           component: () => import('@/views/admin/DashboardView.vue'),
           meta: {
             title: '管理仪表板',
             permissions: ['admin.dashboard.view'],
             breadcrumb: [
               { title: '管理后台', path: '/admin' },
               { title: '仪表板' }
             ]
           }
         },
         {
           path: 'models',
           name: 'ModelManage',
           component: () => import('@/views/admin/ModelManageView.vue'),
           meta: {
             title: '模型管理',
             permissions: ['admin.models.manage'],
             breadcrumb: [
               { title: '管理后台', path: '/admin' },
               { title: '模型管理' }
             ]
           }
         },
         {
           path: 'users',
           name: 'UserManage',
           component: () => import('@/views/admin/UserManageView.vue'),
           meta: {
             title: '用户管理',
             permissions: ['admin.users.manage']
           }
         }
       ]
     }
   ]
   ```

5. **路由汇总配置**
   ```typescript
   // router/routes/index.ts
   import { authRoutes } from './auth'
   import { chatRoutes } from './chat'
   import { adminRoutes } from './admin'
   import { profileRoutes } from './profile'
   import { errorRoutes } from './error'
   import { devRoutes } from './dev'
   
   export const routes: RouteRecordRaw[] = [
     {
       path: '/',
       redirect: '/chat'
     },
     ...authRoutes,
     ...chatRoutes,
     ...adminRoutes,
     ...profileRoutes,
     ...errorRoutes,
     ...(import.meta.env.DEV ? devRoutes : [])
   ]
   ```

#### **Day 5: 路由守卫和中间件**
**上午 (4小时):**
1. **全局路由守卫**
   ```typescript
   // router/guards.ts
   import type { Router } from 'vue-router'
   import { useAuth, usePermission, useNotification } from '@/composables'
   
   export function setupRouterGuards(router: Router) {
     // 全局前置守卫
     router.beforeEach(async (to, from, next) => {
       const { isAuthenticated, checkAuth } = useAuth()
       const { hasPermission } = usePermission()
       const { notify } = useNotification()
       
       // 1. 认证检查
       if (to.meta.requiresAuth && !isAuthenticated.value) {
         await checkAuth()
         
         if (!isAuthenticated.value) {
           notify.warning('请先登录')
           return next({
             name: 'Login',
             query: { redirect: to.fullPath }
           })
         }
       }
       
       // 2. 权限检查
       if (to.meta.permissions?.length && isAuthenticated.value) {
         const hasRequiredPermission = to.meta.permissions.every(
           permission => hasPermission(permission)
         )
         
         if (!hasRequiredPermission) {
           notify.error('权限不足')
           return next({ name: 'Forbidden' })
         }
       }
       
       // 3. 角色检查
       if (to.meta.roles?.length && isAuthenticated.value) {
         const { currentUser } = useAuth()
         const hasRequiredRole = to.meta.roles.some(
           role => currentUser.value?.roles.includes(role)
         )
         
         if (!hasRequiredRole) {
           notify.error('角色权限不足')
           return next({ name: 'Forbidden' })
         }
       }
       
       next()
     })
     
     // 全局后置守卫
     router.afterEach((to) => {
       // 更新页面标题
       if (to.meta.title) {
         document.title = `${to.meta.title} - AI智能对话系统`
       }
       
       // 页面访问统计
       if (import.meta.env.PROD) {
         // 发送页面访问统计
         trackPageView(to.path)
       }
     })
     
     // 路由错误处理
     router.onError((error) => {
       console.error('路由错误:', error)
       const { notify } = useNotification()
       notify.error('页面加载失败，请重试')
     })
   }
   ```

2. **路由中间件系统**
   ```typescript
   // router/middleware.ts
   import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
   
   export type MiddlewareFunction = (
     to: RouteLocationNormalized,
     from: RouteLocationNormalized,
     next: NavigationGuardNext
   ) => void | Promise<void>
   
   // 认证中间件
   export const authMiddleware: MiddlewareFunction = async (to, from, next) => {
     const { isAuthenticated, checkAuth } = useAuth()
     
     if (!isAuthenticated.value) {
       await checkAuth()
     }
     
     if (isAuthenticated.value) {
       next()
     } else {
       next({ name: 'Login', query: { redirect: to.fullPath } })
     }
   }
   
   // 权限中间件工厂
   export const permissionMiddleware = (permissions: string[]): MiddlewareFunction => {
     return (to, from, next) => {
       const { hasAllPermissions } = usePermission()
       
       if (hasAllPermissions(permissions)) {
         next()
       } else {
         next({ name: 'Forbidden' })
       }
     }
   }
   
   // 访客中间件（已登录用户重定向）
   export const guestMiddleware: MiddlewareFunction = (to, from, next) => {
     const { isAuthenticated } = useAuth()
     
     if (isAuthenticated.value) {
       next({ name: 'Chat' })
     } else {
       next()
     }
   }
   ```

**下午 (4小时):**
3. **路由器主配置**
   ```typescript
   // router/index.ts
   import { createRouter, createWebHistory } from 'vue-router'
   import { setupRouterGuards } from './guards'
   import { routes } from './routes'
   
   const router = createRouter({
     history: createWebHistory(import.meta.env.BASE_URL),
     routes,
     scrollBehavior(to, from, savedPosition) {
       if (savedPosition) {
         return savedPosition
       } else if (to.hash) {
         return { el: to.hash, behavior: 'smooth' }
       } else {
         return { top: 0, behavior: 'smooth' }
       }
     }
   })
   
   // 设置路由守卫
   setupRouterGuards(router)
   
   export default router
   ```

4. **路由功能完善**
   ```typescript
   // composables/useRouter.ts
   import { useRouter as useVueRouter, useRoute } from 'vue-router'
   import type { RouteLocationRaw } from 'vue-router'
   
   export function useAppRouter() {
     const router = useVueRouter()
     const route = useRoute()
     
     // 安全导航（带错误处理）
     const navigateTo = async (to: RouteLocationRaw) => {
       try {
         await router.push(to)
       } catch (error) {
         console.error('路由跳转失败:', error)
       }
     }
     
     // 返回上一页
     const goBack = () => {
       if (window.history.length > 1) {
         router.back()
       } else {
         router.push('/')
       }
     }
     
     // 面包屑导航
     const breadcrumb = computed(() => {
       return route.meta.breadcrumb || []
     })
     
     // 页面标题
     const pageTitle = computed(() => {
       return route.meta.title || '未知页面'
     })
     
     return {
       router,
       route,
       navigateTo,
       goBack,
       breadcrumb,
       pageTitle
     }
   }
   ```

5. **路由测试和优化**
   - 路由懒加载测试
   - 权限控制测试
   - 重定向逻辑测试
   - 性能优化和预加载

### ✅ Phase 9 验收标准
- [ ] 所有路由配置正确且可访问
- [ ] 权限控制系统正常工作
- [ ] 路由守卫和中间件功能完整
- [ ] 面包屑导航和页面标题正确
- [ ] 路由懒加载和性能优化良好
- [ ] 错误处理和异常情况覆盖完整

---

## 📊 Phase 6-9 总结和集成

### 🎯 完成目标
经过Phase 6-9的开发，将实现：
- ✅ **完整的组件库系统** (8个基础组件 + 7个业务组件)
- ✅ **功能完整的页面系统** (认证、聊天、管理、用户、错误页面)
- ✅ **智能的路由权限控制** (基于角色和权限的访问控制)
- ✅ **响应式的用户界面** (支持多设备和多主题)

### 🔗 技术集成点
1. **与Phase 4-5的无缝集成**
   - 组件直接使用已验证的composables
   - 页面基于完善的状态管理系统
   - 路由集成权限控制composable

2. **性能优化策略**
   - 组件和页面懒加载
   - 虚拟滚动处理大量数据
   - 智能缓存和预加载
   - 代码分割和bundle优化

3. **用户体验提升**
   - 流畅的页面转场动画
   - 智能的错误处理和反馈
   - 响应式设计和无障碍支持
   - 直观的导航和面包屑

### 📅 时间安排总结
- **第3周**: Phase 6-7 组件开发 (5天)
- **第4周**: Phase 8-9 页面和路由 (5天)
- **总计**: 10个工作日完成完整前端应用

### 🚀 最终交付物
一个功能完整、性能优良、用户体验佳的企业级AI对话系统前端应用，包含：
- 完整的用户认证和权限管理
- 流畅的实时聊天体验
- 强大的模型管理功能
- 直观的管理后台界面
- 响应式的多设备支持
- 完善的错误处理机制
