<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能对话系统</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        :root {
            --primary: #4f46e5;
            --primary-dark: #3730a3;
            --secondary: #8b5cf6;
            --accent: #06b6d4;
            --success: #10b981;
            --warning: #f59e0b;
            --error: #ef4444;
            --text: #1f2937;
            --text-light: #6b7280;
            --text-lighter: #9ca3af;
            --bg: #f8fafc;
            --card-bg: rgba(255, 255, 255, 0.9);
            --sidebar-bg: rgba(248, 250, 252, 0.95);
            --border: rgba(0, 0, 0, 0.1);
            --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --blur: blur(12px);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .dark-mode {
            --text: #f9fafb;
            --text-light: #d1d5db;
            --text-lighter: #9ca3af;
            --bg: #0f172a;
            --card-bg: rgba(30, 41, 59, 0.9);
            --sidebar-bg: rgba(15, 23, 42, 0.95);
            --border: rgba(255, 255, 255, 0.1);
            --shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3);
        }

        body {
            background: var(--bg);
            color: var(--text);
            min-height: 100vh;
            transition: var(--transition);
            background-image: 
                radial-gradient(circle at 10% 20%, rgba(79, 70, 229, 0.05) 0%, transparent 20%),
                radial-gradient(circle at 90% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 20%);
        }

        .app-container {
            display: flex;
            height: 100vh;
            max-width: 1600px;
            margin: 0 auto;
            position: relative;
        }

        /* 登录界面 */
        .login-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            width: 100%;
            background: var(--bg);
        }

        .login-card {
            background: var(--card-bg);
            backdrop-filter: var(--blur);
            border-radius: 24px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            box-shadow: var(--shadow);
            border: 1px solid var(--border);
            text-align: center;
            transform: translateY(0);
            transition: var(--transition);
        }

        .login-card.hidden {
            transform: translateY(20px);
            opacity: 0;
            pointer-events: none;
        }

        .logo {
            font-size: 2.5rem;
            margin-bottom: 10px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-weight: 800;
        }

        .logo-icon {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .input-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .input-group label {
            display: block;
            margin-bottom: 8px;
            color: var(--text-light);
            font-size: 0.9rem;
        }

        .input-group input {
            width: 100%;
            padding: 14px;
            border-radius: 12px;
            border: 1px solid var(--border);
            background: rgba(255, 255, 255, 0.1);
            color: var(--text);
            font-size: 1rem;
            transition: var(--transition);
        }

        .input-group input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
        }

        .btn {
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            border: none;
            border-radius: 12px;
            padding: 14px 20px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: var(--transition);
            width: 100%;
            margin-top: 10px;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn.loading::after {
            content: "";
            position: absolute;
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            right: 14px;
            top: 50%;
            transform: translateY(-50%);
        }

        @keyframes spin {
            to { transform: translateY(-50%) rotate(360deg); }
        }

        /* 主界面 */
        .main-interface {
            display: flex;
            width: 100%;
            opacity: 0;
            transform: scale(0.95);
            transition: var(--transition);
        }

        .main-interface.active {
            opacity: 1;
            transform: scale(1);
        }

        /* 侧边栏 */
        .sidebar {
            width: 320px;
            background: var(--sidebar-bg);
            backdrop-filter: var(--blur);
            border-right: 1px solid var(--border);
            display: flex;
            flex-direction: column;
            transition: var(--transition);
            z-index: 10;
        }

        .user-info {
            padding: 20px;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
        }

        .user-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 1.2rem;
            margin-right: 15px;
        }

        .user-details {
            flex: 1;
        }

        .user-name {
            font-weight: 600;
            margin-bottom: 4px;
        }

        .user-status {
            color: var(--success);
            font-size: 0.8rem;
            display: flex;
            align-items: center;
        }

        .user-status::before {
            content: "";
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: var(--success);
            margin-right: 6px;
        }

        /* 模型选择区域 */
        .model-section {
            padding: 20px;
            border-bottom: 1px solid var(--border);
        }

        .model-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }

        .model-title {
            font-weight: 600;
            font-size: 1rem;
        }

        .refresh-models {
            background: rgba(79, 70, 229, 0.1);
            color: var(--primary);
            border: none;
            border-radius: 8px;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
        }

        .refresh-models:hover {
            background: rgba(79, 70, 229, 0.2);
            transform: rotate(90deg);
        }

        .model-select {
            width: 100%;
            padding: 12px;
            border-radius: 10px;
            border: 1px solid var(--border);
            background: var(--card-bg);
            color: var(--text);
            font-size: 0.9rem;
            transition: var(--transition);
        }

        .model-select:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        }

        .model-info {
            margin-top: 12px;
            padding: 12px;
            background: rgba(79, 70, 229, 0.05);
            border-radius: 8px;
            font-size: 0.8rem;
            color: var(--text-light);
        }

        /* 对话列表 */
        .conversation-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .conversation-header {
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .conversation-title {
            font-weight: 600;
            font-size: 1.1rem;
        }

        .new-chat-btn {
            background: var(--primary);
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            box-shadow: 0 4px 10px rgba(79, 70, 229, 0.3);
            border: none;
        }

        .new-chat-btn:hover {
            transform: rotate(90deg) scale(1.1);
        }

        .conversation-list {
            flex: 1;
            overflow-y: auto;
            padding: 0 10px;
        }

        .conversation-item {
            padding: 14px 16px;
            border-radius: 12px;
            margin-bottom: 8px;
            cursor: pointer;
            transition: var(--transition);
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .conversation-item:hover {
            background: rgba(79, 70, 229, 0.1);
        }

        .conversation-item.active {
            background: rgba(79, 70, 229, 0.15);
            box-shadow: 0 0 0 2px var(--primary);
        }

        .conversation-icon {
            margin-right: 12px;
            color: var(--primary);
            font-size: 1.1rem;
        }

        .conversation-content {
            flex: 1;
            min-width: 0;
        }

        .conversation-name {
            font-weight: 500;
            margin-bottom: 4px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .conversation-preview {
            font-size: 0.85rem;
            color: var(--text-light);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* 聊天区域 */
        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: var(--bg);
            position: relative;
        }

        .chat-header {
            padding: 16px 24px;
            border-bottom: 1px solid var(--border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: var(--card-bg);
            backdrop-filter: var(--blur);
            z-index: 5;
        }

        .chat-title {
            font-size: 1.2rem;
            font-weight: 600;
        }

        .chat-actions {
            display: flex;
            gap: 15px;
        }

        .action-btn {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(79, 70, 229, 0.1);
            color: var(--primary);
            cursor: pointer;
            transition: var(--transition);
            border: none;
        }

        .action-btn:hover {
            background: var(--primary);
            color: white;
            transform: translateY(-2px);
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .message {
            max-width: 80%;
            padding: 16px 20px;
            border-radius: 18px;
            position: relative;
            animation: messageAppear 0.3s ease forwards;
            opacity: 0;
            transform: translateY(10px);
            box-shadow: var(--shadow);
        }

        @keyframes messageAppear {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.user {
            align-self: flex-end;
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            border-bottom-right-radius: 4px;
        }

        .message.ai {
            align-self: flex-start;
            background: var(--card-bg);
            backdrop-filter: var(--blur);
            border: 1px solid var(--border);
            border-bottom-left-radius: 4px;
        }

        .message-content {
            line-height: 1.6;
            word-wrap: break-word;
        }

        .message-time {
            font-size: 0.7rem;
            opacity: 0.7;
            margin-top: 8px;
            text-align: right;
        }

        .message.ai .message-time {
            text-align: left;
        }

        .typing-indicator {
            display: flex;
            align-items: center;
            padding: 16px 20px;
            background: var(--card-bg);
            border: 1px solid var(--border);
            border-radius: 18px;
            border-bottom-left-radius: 4px;
            width: fit-content;
            margin-top: 10px;
            backdrop-filter: var(--blur);
            box-shadow: var(--shadow);
        }

        .typing-dot {
            width: 8px;
            height: 8px;
            background: var(--text-light);
            border-radius: 50%;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }

        .typing-dot:nth-child(1) { animation-delay: 0s; }
        .typing-dot:nth-child(2) { animation-delay: 0.2s; }
        .typing-dot:nth-child(3) { animation-delay: 0.4s; }

        @keyframes typing {
            0%, 60%, 100% { transform: translateY(0); }
            30% { transform: translateY(-5px); }
        }

        .chat-input-container {
            padding: 20px;
            background: var(--card-bg);
            backdrop-filter: var(--blur);
            border-top: 1px solid var(--border);
        }

        .input-area {
            display: flex;
            gap: 12px;
            align-items: flex-end;
        }

        .message-input {
            flex: 1;
            padding: 16px 20px;
            border-radius: 16px;
            border: 1px solid var(--border);
            background: rgba(255, 255, 255, 0.1);
            color: var(--text);
            font-size: 1rem;
            resize: none;
            max-height: 150px;
            transition: var(--transition);
            font-family: inherit;
        }

        .message-input:focus {
            outline: none;
            border-color: var(--primary);
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
        }

        .send-btn {
            width: 48px;
            height: 48px;
            border-radius: 14px;
            background: linear-gradient(135deg, var(--primary), var(--primary-dark));
            color: white;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: var(--transition);
            font-size: 1.2rem;
            box-shadow: 0 4px 15px rgba(79, 70, 229, 0.3);
        }

        .send-btn:hover:not(:disabled) {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4);
        }

        .send-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        /* RAG 开关 */
        .rag-toggle {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding: 12px;
            background: rgba(139, 92, 246, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(139, 92, 246, 0.2);
        }

        .rag-switch {
            position: relative;
            width: 48px;
            height: 24px;
            background: var(--border);
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            margin-right: 12px;
        }

        .rag-switch.active {
            background: var(--secondary);
        }

        .rag-switch::after {
            content: "";
            position: absolute;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            top: 2px;
            left: 2px;
            transition: var(--transition);
        }

        .rag-switch.active::after {
            transform: translateX(24px);
        }

        .rag-label {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--secondary);
        }

        /* 主题切换 */
        .theme-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 26px;
            background: var(--card-bg);
            backdrop-filter: var(--blur);
            border-radius: 13px;
            border: 1px solid var(--border);
            cursor: pointer;
            z-index: 100;
            box-shadow: var(--shadow);
            display: flex;
            align-items: center;
            padding: 0 3px;
        }

        .toggle-handle {
            width: 20px;
            height: 20px;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            border-radius: 50%;
            transition: var(--transition);
        }

        .dark-mode .toggle-handle {
            transform: translateX(24px);
        }

        /* 错误提示 */
        .error-message {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error);
            padding: 12px;
            border-radius: 8px;
            margin-top: 12px;
            font-size: 0.9rem;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .success-message {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success);
            padding: 12px;
            border-radius: 8px;
            margin-top: 12px;
            font-size: 0.9rem;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                position: absolute;
                left: -320px;
                height: 100%;
                top: 0;
                z-index: 20;
                box-shadow: 10px 0 30px rgba(0, 0, 0, 0.1);
            }
            
            .sidebar.active {
                left: 0;
            }
            
            .mobile-menu-btn {
                display: flex;
            }
            
            .message {
                max-width: 90%;
            }
        }

        .mobile-menu-btn {
            position: absolute;
            top: 20px;
            left: 20px;
            width: 40px;
            height: 40px;
            border-radius: 12px;
            background: var(--card-bg);
            backdrop-filter: var(--blur);
            border: 1px solid var(--border);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 15;
            cursor: pointer;
            box-shadow: var(--shadow);
        }

        .hidden {
            display: none !important;
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: transparent;
        }

        ::-webkit-scrollbar-thumb {
            background: var(--border);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--text-lighter);
        }
    </style>
</head>
<body>
    <!-- 主题切换按钮 -->
    <div class="theme-toggle" id="theme-toggle">
        <div class="toggle-handle"></div>
    </div>

    <!-- 登录界面 -->
    <div class="login-container" id="login-container">
        <div class="login-card" id="login-card">
            <div class="logo-icon">
                <i class="fas fa-robot"></i>
            </div>
            <div class="logo">AI智能对话</div>
            <p style="color: var(--text-light); margin-bottom: 30px;">连接未来，智慧对话</p>
            
            <div class="input-group">
                <label for="username">用户名</label>
                <input type="text" id="username" placeholder="输入您的用户名" value="admin">
            </div>
            
            <div class="input-group">
                <label for="password">密码</label>
                <input type="password" id="password" placeholder="输入您的密码" value="admin">
            </div>
            
            <button class="btn" id="login-btn">
                登录
            </button>
            
            <div id="login-error" class="error-message hidden"></div>
            
            <div style="margin-top: 20px; font-size: 0.9rem; color: var(--text-light);">
                还没有账号? <a href="#" style="color: var(--primary); text-decoration: none;">立即注册</a>
            </div>
        </div>
    </div>

    <!-- 主界面 -->
    <div class="app-container">
        <div class="main-interface" id="main-interface">
            <!-- 移动端菜单按钮 -->
            <div class="mobile-menu-btn" id="menu-btn">
                <i class="fas fa-bars"></i>
            </div>
            
            <!-- 侧边栏 -->
            <div class="sidebar" id="sidebar">
                <div class="user-info">
                    <div class="user-avatar" id="user-avatar">U</div>
                    <div class="user-details">
                        <div class="user-name" id="user-name">用户</div>
                        <div class="user-status">在线</div>
                    </div>
                </div>
                
                <!-- 模型选择 -->
                <div class="model-section">
                    <div class="model-header">
                        <div class="model-title">AI模型</div>
                        <button class="refresh-models" id="refresh-models">
                            <i class="fas fa-sync-alt"></i>
                        </button>
                    </div>
                    <select class="model-select" id="model-select">
                        <option value="">加载中...</option>
                    </select>
                    <div class="model-info" id="model-info">
                        请选择一个AI模型开始对话
                    </div>
                </div>

                <!-- RAG 开关 -->
                <div class="model-section">
                    <div class="rag-toggle">
                        <div class="rag-switch" id="rag-switch"></div>
                        <div class="rag-label">启用RAG增强</div>
                    </div>
                </div>
                
                <!-- 对话历史 -->
                <div class="conversation-section">
                    <div class="conversation-header">
                        <div class="conversation-title">对话历史</div>
                        <button class="new-chat-btn" id="new-chat-btn">
                            <i class="fas fa-plus"></i>
                        </button>
                    </div>
                    
                    <div class="conversation-list" id="conversation-list">
                        <!-- 对话列表将动态加载 -->
                    </div>
                </div>
            </div>
            
            <!-- 聊天区域 -->
            <div class="chat-container">
                <div class="chat-header">
                    <div class="chat-title" id="chat-title">新对话</div>
                    <div class="chat-actions">
                        <button class="action-btn" id="clear-chat" title="清空对话">
                            <i class="fas fa-trash"></i>
                        </button>
                        <button class="action-btn" id="save-chat" title="保存对话">
                            <i class="fas fa-save"></i>
                        </button>
                        <button class="action-btn" id="settings-btn" title="设置">
                            <i class="fas fa-cog"></i>
                        </button>
                    </div>
                </div>
                
                <div class="chat-messages" id="chat-messages">
                    <div class="message ai">
                        <div class="message-content">
                            您好！我是AI智能助手，有什么可以帮助您的吗？
                        </div>
                        <div class="message-time" id="welcome-time"></div>
                    </div>
                </div>
                
                <div class="chat-input-container">
                    <div class="input-area">
                        <textarea class="message-input" id="message-input" 
                                placeholder="输入消息..." rows="1"></textarea>
                        <button class="send-btn" id="send-btn">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API配置
        const API_BASE = 'http://localhost:8000/api/v1';
        let authToken = null;
        let currentConversationId = null;
        let currentModelId = null;
        let ragEnabled = false;
        let isStreaming = false;

        // 工具函数
        function formatTime(date = new Date()) {
            return date.toLocaleTimeString('zh-CN', { 
                hour: '2-digit', 
                minute: '2-digit' 
            });
        }

        function showError(message, elementId = 'login-error') {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
            setTimeout(() => {
                errorElement.classList.add('hidden');
            }, 5000);
        }

        function showSuccess(message, elementId) {
            const element = document.getElementById(elementId);
            element.className = 'success-message';
            element.textContent = message;
            element.classList.remove('hidden');
            setTimeout(() => {
                element.classList.add('hidden');
            }, 3000);
        }

        // API调用函数
        async function apiCall(endpoint, options = {}) {
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    ...(authToken && { 'Authorization': `Bearer ${authToken}` }),
                    ...options.headers
                },
                ...options
            };

            try {
                const response = await fetch(`${API_BASE}${endpoint}`, config);
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(errorData.detail || `HTTP ${response.status}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('API调用失败:', error);
                throw error;
            }
        }

        // 登录功能
        async function login(username, password) {
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.detail || '登录失败');
                }

                const data = await response.json();
                authToken = data.access_token;
                
                // 获取用户信息
                const userInfo = await apiCall('/auth/me');
                
                // 更新UI
                document.getElementById('user-name').textContent = userInfo.full_name || userInfo.username;
                document.getElementById('user-avatar').textContent = 
                    (userInfo.full_name || userInfo.username).charAt(0).toUpperCase();
                
                return true;
            } catch (error) {
                throw error;
            }
        }

        // 加载可用模型
        async function loadModels() {
            try {
                const models = await apiCall('/chat/models/');
                const modelSelect = document.getElementById('model-select');
                
                modelSelect.innerHTML = '<option value="">请选择模型</option>';
                
                models.forEach(model => {
                    const option = document.createElement('option');
                    option.value = model.id;
                    option.textContent = model.display_name || model.name;
                    option.dataset.description = model.description;
                    option.dataset.capabilities = JSON.stringify(model.capabilities);
                    modelSelect.appendChild(option);
                });

                // 默认选择第一个模型
                if (models.length > 0) {
                    modelSelect.value = models[0].id;
                    currentModelId = models[0].id;
                    updateModelInfo(models[0]);
                }
            } catch (error) {
                console.error('加载模型失败:', error);
                showError('加载模型失败: ' + error.message);
            }
        }

        // 更新模型信息显示
        function updateModelInfo(model) {
            const modelInfo = document.getElementById('model-info');
            if (model) {
                modelInfo.innerHTML = `
                    <strong>${model.display_name || model.name}</strong><br>
                    ${model.description || '暂无描述'}
                `;
            } else {
                modelInfo.textContent = '请选择一个AI模型开始对话';
            }
        }

        // 加载对话历史
        async function loadConversations() {
            try {
                const conversations = await apiCall('/chat/conversations/');
                const conversationList = document.getElementById('conversation-list');
                
                conversationList.innerHTML = '';
                
                conversations.forEach(conv => {
                    const item = document.createElement('div');
                    item.className = 'conversation-item';
                    item.dataset.id = conv.id;
                    
                    item.innerHTML = `
                        <div class="conversation-icon">
                            <i class="fas fa-comment"></i>
                        </div>
                        <div class="conversation-content">
                            <div class="conversation-name">${conv.title}</div>
                            <div class="conversation-preview">${conv.last_message || '暂无消息'}</div>
                        </div>
                    `;
                    
                    item.addEventListener('click', () => loadConversation(conv.id));
                    conversationList.appendChild(item);
                });
            } catch (error) {
                console.error('加载对话历史失败:', error);
            }
        }

        // 加载特定对话
        async function loadConversation(conversationId) {
            try {
                const conversation = await apiCall(`/chat/conversations/${conversationId}`);
                currentConversationId = conversationId;
                
                // 更新UI
                document.getElementById('chat-title').textContent = conversation.title;
                const chatMessages = document.getElementById('chat-messages');
                chatMessages.innerHTML = '';
                
                // 显示消息历史
                conversation.messages.forEach(message => {
                    addMessageToUI(message.content, message.role, new Date(message.created_at));
                });
                
                // 更新对话列表选中状态
                document.querySelectorAll('.conversation-item').forEach(item => {
                    item.classList.remove('active');
                });
                document.querySelector(`[data-id="${conversationId}"]`)?.classList.add('active');
                
            } catch (error) {
                console.error('加载对话失败:', error);
                showError('加载对话失败: ' + error.message);
            }
        }

        // 创建新对话
        function newConversation() {
            currentConversationId = null;
            document.getElementById('chat-title').textContent = '新对话';
            document.getElementById('chat-messages').innerHTML = `
                <div class="message ai">
                    <div class="message-content">
                        您好！我是AI智能助手，有什么可以帮助您的吗？
                    </div>
                    <div class="message-time">${formatTime()}</div>
                </div>
            `;
            
            // 清除选中状态
            document.querySelectorAll('.conversation-item').forEach(item => {
                item.classList.remove('active');
            });
        }

        // 添加消息到UI
        function addMessageToUI(content, role, timestamp = new Date()) {
            const chatMessages = document.getElementById('chat-messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${role}`;
            
            messageDiv.innerHTML = `
                <div class="message-content">${content}</div>
                <div class="message-time">${formatTime(timestamp)}</div>
            `;
            
            chatMessages.appendChild(messageDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 显示打字指示器
        function showTypingIndicator() {
            const chatMessages = document.getElementById('chat-messages');
            const typingDiv = document.createElement('div');
            typingDiv.className = 'typing-indicator';
            typingDiv.id = 'typing-indicator';
            
            typingDiv.innerHTML = `
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            `;
            
            chatMessages.appendChild(typingDiv);
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // 隐藏打字指示器
        function hideTypingIndicator() {
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // 发送消息
        async function sendMessage(content) {
            if (!content.trim() || !currentModelId) {
                if (!currentModelId) {
                    showError('请先选择一个AI模型');
                }
                return;
            }

            if (isStreaming) {
                return;
            }

            // 添加用户消息到UI
            addMessageToUI(content, 'user');
            
            // 清空输入框
            document.getElementById('message-input').value = '';
            
            // 禁用发送按钮
            const sendBtn = document.getElementById('send-btn');
            sendBtn.disabled = true;
            isStreaming = true;
            
            // 显示打字指示器
            showTypingIndicator();

            try {
                const messageData = {
                    content: content,
                    conversation_id: currentConversationId,
                    model_id: currentModelId,
                    use_rag: ragEnabled
                };

                const response = await apiCall('/chat/messages/', {
                    method: 'POST',
                    body: JSON.stringify(messageData)
                });

                // 隐藏打字指示器
                hideTypingIndicator();
                
                // 添加AI回复到UI
                addMessageToUI(response.content, 'ai');
                
                // 更新当前对话ID
                if (!currentConversationId && response.conversation_id) {
                    currentConversationId = response.conversation_id;
                    // 重新加载对话列表
                    loadConversations();
                }

            } catch (error) {
                hideTypingIndicator();
                console.error('发送消息失败:', error);
                addMessageToUI('抱歉，发送消息时出现错误: ' + error.message, 'ai');
            } finally {
                sendBtn.disabled = false;
                isStreaming = false;
            }
        }

        // 自适应文本区域高度
        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 150) + 'px';
        }

        // 初始化事件监听器
        function initializeEventListeners() {
            // 登录按钮
            document.getElementById('login-btn').addEventListener('click', async () => {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                
                if (!username || !password) {
                    showError('请输入用户名和密码');
                    return;
                }

                const loginBtn = document.getElementById('login-btn');
                loginBtn.disabled = true;
                loginBtn.classList.add('loading');
                loginBtn.textContent = '登录中...';

                try {
                    await login(username, password);
                    
                    // 隐藏登录界面，显示主界面
                    document.getElementById('login-card').classList.add('hidden');
                    setTimeout(() => {
                        document.getElementById('login-container').classList.add('hidden');
                        document.getElementById('main-interface').classList.add('active');
                    }, 500);
                    
                    // 加载初始数据
                    await Promise.all([
                        loadModels(),
                        loadConversations()
                    ]);
                    
                } catch (error) {
                    showError('登录失败: ' + error.message);
                } finally {
                    loginBtn.disabled = false;
                    loginBtn.classList.remove('loading');
                    loginBtn.textContent = '登录';
                }
            });

            // 主题切换
            document.getElementById('theme-toggle').addEventListener('click', () => {
                document.body.classList.toggle('dark-mode');
                localStorage.setItem('dark-mode', document.body.classList.contains('dark-mode'));
            });

            // 移动端菜单
            document.getElementById('menu-btn').addEventListener('click', () => {
                document.getElementById('sidebar').classList.toggle('active');
            });

            // 刷新模型
            document.getElementById('refresh-models').addEventListener('click', loadModels);

            // 模型选择
            document.getElementById('model-select').addEventListener('change', (e) => {
                currentModelId = e.target.value;
                const selectedOption = e.target.selectedOptions[0];
                if (selectedOption && selectedOption.value) {
                    const model = {
                        id: selectedOption.value,
                        name: selectedOption.textContent,
                        display_name: selectedOption.textContent,
                        description: selectedOption.dataset.description,
                        capabilities: JSON.parse(selectedOption.dataset.capabilities || '[]')
                    };
                    updateModelInfo(model);
                } else {
                    updateModelInfo(null);
                }
            });

            // RAG开关
            document.getElementById('rag-switch').addEventListener('click', (e) => {
                ragEnabled = !ragEnabled;
                e.target.classList.toggle('active', ragEnabled);
            });

            // 新对话
            document.getElementById('new-chat-btn').addEventListener('click', newConversation);

            // 清空对话
            document.getElementById('clear-chat').addEventListener('click', () => {
                if (confirm('确定要清空当前对话吗？')) {
                    newConversation();
                }
            });

            // 发送消息
            document.getElementById('send-btn').addEventListener('click', () => {
                const input = document.getElementById('message-input');
                sendMessage(input.value);
            });

            // 输入框事件
            const messageInput = document.getElementById('message-input');
            messageInput.addEventListener('input', (e) => {
                autoResizeTextarea(e.target);
            });

            messageInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage(e.target.value);
                }
            });

            // 按Enter键登录
            document.getElementById('password').addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    document.getElementById('login-btn').click();
                }
            });
        }

        // 初始化应用
        function initializeApp() {
            // 设置欢迎时间
            document.getElementById('welcome-time').textContent = formatTime();
            
            // 恢复主题设置
            if (localStorage.getItem('dark-mode') === 'true') {
                document.body.classList.add('dark-mode');
            }
            
            // 初始化事件监听器
            initializeEventListeners();
            
            console.log('AI智能对话系统已启动');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>