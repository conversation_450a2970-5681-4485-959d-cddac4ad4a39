[project]
name = "azure-calculator-backend"
version = "0.0.1"
description = "Azure Calculator backend API"
authors = [
    {name = "Shuo Lv", email = "<EMAIL>"}
]
readme = "README.md"
package-mode = false
requires-python = ">=3.10, <3.13"
dependencies = [
    "fastapi (>=0.115.12,<0.116.0)",
    "uvicorn (>=0.34.0,<0.35.0)",
    "pydantic[email] (>=2.11.3,<3.0.0)",
    "pydantic-settings (>=2.8.1,<3.0.0)",
    "sqlalchemy (>=2.0.40,<3.0.0)",
    "alembic (>=1.15.2,<2.0.0)",
    "asyncpg (>=0.30.0,<0.31.0)",
    "python-jose (>=3.4.0,<4.0.0)",
    "passlib (>=1.7.4,<2.0.0)",
    "python-multipart (>=0.0.20,<0.0.21)",
    "celery (>=5.5.0,<6.0.0)",
    "redis (>=5.2.1,<6.0.0)",
    "httpx (>=0.28.1,<0.29.0)",
    "tenacity (>=9.1.2,<10.0.0)",
    "loguru (>=0.7.3,<0.8.0)",
    "psycopg2-binary (>=2.9.10,<3.0.0)",
    "openai (>=1.70.0,<2.0.0)",
    "anthropic (>=0.49.0,<0.50.0)",
    "python-dotenv (>=1.1.0,<2.0.0)",
    "ldap3 (>=2.9.1,<3.0.0)",
    "jinja2 (>=3.1.6,<4.0.0)",
    "playwright (>=1.51.0,<2.0.0)",
    "trafilatura (>=2.0.0,<3.0.0)",
    "beautifulsoup4 (>=4.13.3,<5.0.0)",
    "tiktoken (>=0.9.0,<0.10.0)",
    "scikit-learn (>=1.6.1,<2.0.0)",
    "networkx (>=3.4.2,<4.0.0)",
    "rank-bm25 (>=0.2.2,<0.3.0)",
    "llama-index-core (>=0.12.25,<0.13.0)",
    "llama-index-readers-web (>=0.3.9,<0.4.0)",
    "llama-index-llms-openai (>=0.3.30,<0.4.0)",
    "llama-index-embeddings-openai (>=0.3.1,<0.4.0)",
    "llama-index-llms-openai-like (>=0.3.4,<0.4.0)",
    "llama-index-vector-stores-qdrant (>=0.6.0,<0.7.0)",
    "llama-index-embeddings-siliconflow (>=0.2.1,<0.3.0)",
    "llama-index-retrievers-bm25 (>=0.5.2,<0.6.0)",
    "llama-index-llms-openrouter (>=0.3.1,<0.4.0)",
    "llama-index-llms-deepseek (>=0.1.1,<0.2.0)",
    "faiss-cpu (>=1.10.0,<2.0.0)",
    "qdrant-client (>=1.13.3,<2.0.0)",
    "pymilvus (>=2.5.6,<3.0.0)",
    "aiofiles (>=24.1.0,<25.0.0)",
    "streamlit (>=1.44.1,<2.0.0)",
    "pandas (>=2.2.3,<3.0.0)",
    "numpy (>=2.2.4,<3.0.0)",
    "matplotlib (>=3.10.1,<4.0.0)",
    "plotly (>=6.0.1,<7.0.0)",
    "pillow (>=11.1.0,<12.0.0)",
    "nest-asyncio (>=1.6.0,<2.0.0)",
    "seaborn (>=0.13.2,<0.14.0)",
    "rich (>=14.0.0,<15.0.0)",
    "cryptography (>=44.0.3,<45.0.0)",
    "bcrypt (>=4.3.0,<5.0.0)",
]

[tool.poetry]
# 指定包目录
packages = [{include = "app"}]

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.group.test.dependencies]
pytest = "^8.3.5"
pytest-asyncio = "^0.26.0"
pytest-cov = "^6.1.0"
black = "^25.1.0"
isort = "^6.0.1"
mypy = "^1.15.0"
ruff = "^0.11.3"
pre-commit = "^4.2.0"

[tool.black]
line-length = 88
target-version = ["py311"]

[tool.isort]
profile = "black"
line_length = 88

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true

[tool.pytest]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
asyncio_mode = "auto"

[tool.ruff]
target-version = "py311"
line-length = 88
select = ["E", "F", "I", "B", "C4", "SIM", "TID", "RUF"]
ignore = []
exclude = [
    ".git",
    ".ruff_cache",
    "__pycache__",
    "alembic",
    ".venv",
]