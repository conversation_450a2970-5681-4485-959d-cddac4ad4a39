"""Add status and additional fields to model_performance_tests

Revision ID: 976469f92c67
Revises: e0847a356030
Create Date: 2025-06-04 16:17:36.540226

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '976469f92c67'
down_revision: Union[str, None] = 'e0847a356030'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('model_performance_tests', sa.Column('status', sa.String(length=20), nullable=False, comment='测试状态: pending, running, completed, failed, timeout, cancelled'))
    op.add_column('model_performance_tests', sa.Column('min_response_time', sa.Float(), nullable=True, comment='最小响应时间(ms)'))
    op.add_column('model_performance_tests', sa.Column('max_response_time', sa.Float(), nullable=True, comment='最大响应时间(ms)'))
    op.add_column('model_performance_tests', sa.Column('total_tokens', sa.Integer(), nullable=True, comment='总token数'))
    op.add_column('model_performance_tests', sa.Column('input_tokens', sa.Integer(), nullable=True, comment='输入token数'))
    op.add_column('model_performance_tests', sa.Column('output_tokens', sa.Integer(), nullable=True, comment='输出token数'))
    op.add_column('model_performance_tests', sa.Column('total_cost', sa.Float(), nullable=True, comment='总成本'))
    op.add_column('model_performance_tests', sa.Column('created_at', sa.DateTime(), nullable=False))
    op.add_column('model_performance_tests', sa.Column('updated_at', sa.DateTime(), nullable=False))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('model_performance_tests', 'updated_at')
    op.drop_column('model_performance_tests', 'created_at')
    op.drop_column('model_performance_tests', 'total_cost')
    op.drop_column('model_performance_tests', 'output_tokens')
    op.drop_column('model_performance_tests', 'input_tokens')
    op.drop_column('model_performance_tests', 'total_tokens')
    op.drop_column('model_performance_tests', 'max_response_time')
    op.drop_column('model_performance_tests', 'min_response_time')
    op.drop_column('model_performance_tests', 'status')
    # ### end Alembic commands ###
