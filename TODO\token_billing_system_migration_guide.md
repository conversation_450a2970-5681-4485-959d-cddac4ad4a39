# Token计费统计系统迁移指南

## 系统概述

AutoCoder的Token计费统计系统是一个完整的LLM使用成本跟踪解决方案，包含以下核心组件：

1. **Token计数器** - 本地和远程Token计数
2. **成本计算器** - 基于模型价格的成本计算
3. **统计数据模型** - 结构化的Token使用统计
4. **存储系统** - SQLite数据库持久化
5. **拦截器** - 自动Token使用跟踪
6. **报告系统** - 格式化输出和日志记录

## 第一阶段：核心数据模型迁移

### 1.1 创建Token统计数据模型

```python
# models/token_usage.py
from pydantic import BaseModel, Field
from typing import Optional

class TokenUsageStats(BaseModel):
    """Token使用统计数据模型"""
    input_tokens: int = Field(default=0, description="输入的token数量")
    output_tokens: int = Field(default=0, description="输出的token数量")
    input_cost: float = Field(default=0.0, description="输入token的成本")
    output_cost: float = Field(default=0.0, description="输出token的成本")

    @property
    def total_tokens(self) -> int:
        """总token数"""
        return self.input_tokens + self.output_tokens

    @property
    def total_cost(self) -> float:
        """总成本"""
        return self.input_cost + self.output_cost
```

### 1.2 创建数据库存储模型

```python
# models/database.py
from sqlmodel import SQLModel, Field, create_engine, Session, select
import os

class TokenCounter(SQLModel, table=True):
    project: str = Field(primary_key=True)
    input_tokens_count: int = 0
    generated_tokens_count: int = 0

class SingletonStore(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            instance = super().__call__(*args, **kwargs)
            cls._instances[cls] = instance
        return cls._instances[cls]

class Store(metaclass=SingletonStore):
    def __init__(self, db_path: str = None):
        self.db_path = db_path or "token_usage.db"
        self.engine = self._create_engine()
        SQLModel.metadata.create_all(self.engine)

    def _create_engine(self):
        if not os.path.exists(self.db_path):
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        return create_engine(f"sqlite:///{self.db_path}")

    def update_token_counter(self, project: str = None, 
                           input_tokens_count: int = 0, 
                           generated_tokens_count: int = 0):
        with Session(self.engine) as session:
            if project is None:
                statement = select(TokenCounter)
            else:
                statement = select(TokenCounter).where(TokenCounter.project == project)
            results = session.exec(statement)
            token_counter = results.first()

            if token_counter is None:
                token_counter = TokenCounter(project=project)

            token_counter.input_tokens_count += input_tokens_count
            token_counter.generated_tokens_count += generated_tokens_count
            session.add(token_counter)
            session.commit()

    def get_token_counter(self, project: str = None):
        with Session(self.engine) as session:
            if project:
                statement = select(TokenCounter).where(TokenCounter.project == project)
            else:
                statement = select(TokenCounter)
            results = session.exec(statement)
            return results.first()
```

## 第二阶段：Token计数器实现

### 2.1 本地Token计数器

```python
# token_counter/local_counter.py
import time
from loguru import logger
from tokenizers import Tokenizer
from multiprocessing import Pool, cpu_count

class VariableHolder:
    TOKENIZER_PATH = None
    TOKENIZER_MODEL = None

def initialize_tokenizer(tokenizer_path):
    global tokenizer_model    
    tokenizer_model = Tokenizer.from_file(tokenizer_path)

def count_tokens(text: str) -> int:       
    try:
        encoded = VariableHolder.TOKENIZER_MODEL.encode(
            '{"role":"user","content":"' + text + '"}')
        return len(encoded.ids)
    except Exception as e:        
        logger.error(f"Error counting tokens: {str(e)}")
        return -1

def count_tokens_worker(text: str) -> int:
    try:
        encoded = tokenizer_model.encode(
            '{"role":"user","content":"' + text + '"}')
        return len(encoded.ids)
    except Exception as e:
        logger.error(f"Error counting tokens: {str(e)}")
        return -1

class TokenCounter:
    def __init__(self, tokenizer_path: str):
        self.tokenizer_path = tokenizer_path
        self.num_processes = cpu_count() - 1 if cpu_count() > 1 else 1
        self.pool = Pool(
            processes=self.num_processes,
            initializer=initialize_tokenizer,
            initargs=(self.tokenizer_path,),
        )

    def count_tokens(self, text: str) -> int:
        return self.pool.apply(count_tokens_worker, (text,))
```

### 2.2 远程Token计数器

```python
# token_counter/remote_counter.py
from loguru import logger

class RemoteTokenCounter:
    def __init__(self, tokenizer) -> None:
        self.tokenizer = tokenizer

    def count_tokens(self, text: str) -> int:
        try:
            v = self.tokenizer.chat_oai(
                conversations=[{"role": "user", "content": text}]
            )
            return int(v[0].output)
        except Exception as e:
            logger.error(f"Error counting tokens: {str(e)}")
            return -1
```

## 第三阶段：成本计算系统

### 3.1 模型价格配置

```python
# config/model_pricing.py
MODEL_PRICING = {
    "gpt-4": {
        "input_price": 30.0,   # 每百万token价格
        "output_price": 60.0
    },
    "gpt-3.5-turbo": {
        "input_price": 1.5,
        "output_price": 2.0
    },
    "claude-3-sonnet": {
        "input_price": 3.0,
        "output_price": 15.0
    }
    # 添加更多模型...
}

def get_model_pricing(model_name: str, product_mode: str = "lite"):
    """获取模型价格信息"""
    return MODEL_PRICING.get(model_name, {
        "input_price": 0.0,
        "output_price": 0.0
    })
```

### 3.2 Token成本计算器

```python
# calculator/token_cost_calculator.py
import time
from typing import Dict, Any, Optional, Tuple
from loguru import logger
from models.token_usage import TokenUsageStats
from config.model_pricing import get_model_pricing

class TokenCostCalculator:
    def __init__(self, logger_name: str = "TokenCostCalculator", 
                 product_mode: str = "lite"):
        self.logger = logger
        self.product_mode = product_mode

    def get_model_info(self, model_name: str, 
                      product_mode: str = "lite") -> Tuple[str, Dict[str, Dict[str, float]]]:
        """获取模型名称和价格信息"""
        model_info_map = {
            model_name: get_model_pricing(model_name, product_mode)
        }
        return model_name, model_info_map

    def calculate_token_costs(self, input_tokens: int, output_tokens: int,
                            model_info_map: Dict[str, Dict[str, float]]) -> TokenUsageStats:
        """计算token统计和成本"""
        stats = TokenUsageStats()
        stats.input_tokens = input_tokens
        stats.output_tokens = output_tokens

        for name, info in model_info_map.items():
            stats.input_cost += (stats.input_tokens * 
                               info.get("input_price", 0.0)) / 1000000
            stats.output_cost += (stats.output_tokens * 
                                info.get("output_price", 0.0)) / 1000000

        return stats

    def track_token_usage(self, model_name: str, input_tokens: int, 
                         output_tokens: int, operation_name: str,
                         start_time: float, end_time: float,
                         product_mode: Optional[str] = None) -> TokenUsageStats:
        """跟踪token使用情况"""
        actual_product_mode = product_mode or self.product_mode
        
        # 获取模型信息
        model_name, model_info_map = self.get_model_info(
            model_name, actual_product_mode)

        # 计算token统计和成本
        stats = self.calculate_token_costs(input_tokens, output_tokens, model_info_map)

        # 记录事件
        self.log_event(
            model_name=model_name,
            operation_name=operation_name,
            start_time=start_time,
            end_time=end_time,
            stats=stats
        )

        return stats

    def log_event(self, model_name: str, operation_name: str,
                 start_time: float, end_time: float, stats: TokenUsageStats):
        """记录token统计信息到日志"""
        elapsed_time = end_time - start_time
        speed = stats.output_tokens / elapsed_time if elapsed_time > 0 else 0

        self.logger.info(f"{operation_name} Stats - Model: {model_name}, "
                        f"Input Tokens: {stats.input_tokens}, "
                        f"Output Tokens: {stats.output_tokens}, "
                        f"Total Tokens: {stats.total_tokens}, "
                        f"Time: {elapsed_time:.2f}s, "
                        f"Speed: {speed:.2f}tokens/s, "
                        f"Total Cost: ${stats.total_cost:.6f}")
```

## 第四阶段：自动拦截器

### 4.1 LLM调用拦截器

```python
# interceptors/token_interceptor.py
from typing import Any, Tuple, Optional
from models.database import Store

def token_counter_interceptor(llm, model: str, response: Any) -> Tuple[bool, Optional[str]]:
    """LLM调用的Token计数拦截器"""
    store = Store()
    
    if isinstance(response, list) and len(response) > 0:
        v = response[0]
        if isinstance(v, dict) and "metadata" in v:
            metadata = v["metadata"]
            input_tokens_count = metadata.get("input_tokens_count", 0)
            generated_tokens_count = metadata.get("generated_tokens_count", 0)
            
            # 更新数据库
            store.update_token_counter(
                project=None,  # 或者从上下文获取项目名
                input_tokens_count=input_tokens_count,
                generated_tokens_count=generated_tokens_count
            )
    
    return True, None

# 使用示例
def setup_interceptor(llm_client):
    """设置拦截器"""
    llm_client.add_callback(token_counter_interceptor)
```

## 第五阶段：报告和显示系统

### 5.1 控制台输出格式化

```python
# display/console_printer.py
from rich.console import Console
from rich.table import Table
from models.token_usage import TokenUsageStats
from models.database import TokenCounter
from typing import List

class ConsolePrinter:
    def __init__(self):
        self.console = Console()

    def print_token_stats(self, operation_name: str, model_name: str,
                         stats: TokenUsageStats, duration: float):
        """打印Token统计信息"""
        speed = stats.output_tokens / duration if duration > 0 else 0
        
        message = (f"{operation_name} - 模型: {model_name}, "
                  f"输入Token: {stats.input_tokens}, "
                  f"输出Token: {stats.output_tokens}, "
                  f"总Token: {stats.total_tokens}, "
                  f"耗时: {duration:.2f}s, "
                  f"速度: {speed:.2f}tokens/s, "
                  f"总成本: ${stats.total_cost:.6f}")
        
        self.console.print(message, style="blue")

    def print_token_table(self, token_counters: List[TokenCounter]):
        """打印Token统计表格"""
        table = Table(title="Token使用统计")
        table.add_column("项目", style="cyan")
        table.add_column("输入Token", style="green")
        table.add_column("输出Token", style="yellow")
        table.add_column("总Token", style="red")

        for counter in token_counters:
            total = counter.input_tokens_count + counter.generated_tokens_count
            table.add_row(
                counter.project or "默认",
                str(counter.input_tokens_count),
                str(counter.generated_tokens_count),
                str(total)
            )

        self.console.print(table)
```

## 第六阶段：集成和使用示例

### 6.1 完整使用示例

```python
# example/usage_example.py
import time
from calculator.token_cost_calculator import TokenCostCalculator
from models.database import Store
from display.console_printer import ConsolePrinter

def example_llm_call_with_tracking():
    """示例：带Token跟踪的LLM调用"""
    
    # 初始化组件
    calculator = TokenCostCalculator()
    store = Store()
    printer = ConsolePrinter()
    
    # 模拟LLM调用
    start_time = time.time()
    
    # 这里是你的LLM调用逻辑
    # response = your_llm.generate(prompt)
    
    # 模拟返回的Token数据
    input_tokens = 1000
    output_tokens = 500
    model_name = "gpt-4"
    
    end_time = time.time()
    
    # 跟踪Token使用
    stats = calculator.track_token_usage(
        model_name=model_name,
        input_tokens=input_tokens,
        output_tokens=output_tokens,
        operation_name="代码生成",
        start_time=start_time,
        end_time=end_time
    )
    
    # 更新数据库
    store.update_token_counter(
        project="my_project",
        input_tokens_count=input_tokens,
        generated_tokens_count=output_tokens
    )
    
    # 显示统计信息
    printer.print_token_stats(
        operation_name="代码生成",
        model_name=model_name,
        stats=stats,
        duration=end_time - start_time
    )
    
    return stats

if __name__ == "__main__":
    example_llm_call_with_tracking()
```

## 第七阶段：配置和初始化

### 7.1 系统初始化

```python
# init/system_init.py
import os
from tokenizers import Tokenizer
from token_counter.local_counter import VariableHolder
from models.database import Store

def initialize_token_system(tokenizer_path: str = None, db_path: str = None):
    """初始化Token计费系统"""
    
    # 初始化Tokenizer
    if tokenizer_path and os.path.exists(tokenizer_path):
        VariableHolder.TOKENIZER_PATH = tokenizer_path
        VariableHolder.TOKENIZER_MODEL = Tokenizer.from_file(tokenizer_path)
    
    # 初始化数据库
    if db_path:
        store = Store(db_path)
    else:
        store = Store()
    
    return store

# 使用示例
def setup_system():
    """设置系统"""
    store = initialize_token_system(
        tokenizer_path="path/to/tokenizer.json",
        db_path="token_usage.db"
    )
    return store
```

## 迁移检查清单

- [ ] 第一阶段：数据模型创建完成
- [ ] 第二阶段：Token计数器实现完成
- [ ] 第三阶段：成本计算系统完成
- [ ] 第四阶段：拦截器集成完成
- [ ] 第五阶段：报告系统完成
- [ ] 第六阶段：集成测试完成
- [ ] 第七阶段：系统初始化完成

## 注意事项

1. **依赖管理**：确保安装必要的依赖包（pydantic, sqlmodel, tokenizers, rich, loguru）
2. **数据库路径**：确保数据库文件路径有写入权限
3. **Tokenizer文件**：确保tokenizer.json文件路径正确
4. **模型价格**：定期更新模型价格配置
5. **错误处理**：添加适当的异常处理和日志记录
6. **性能优化**：对于大量Token计数，考虑使用多进程池
7. **数据备份**：定期备份Token使用数据库

这个迁移指南提供了完整的Token计费统计系统实现，可以根据具体需求进行调整和扩展。

## 附录A：完整项目结构

```
token_billing_system/
├── models/
│   ├── __init__.py
│   ├── token_usage.py          # Token统计数据模型
│   └── database.py             # 数据库模型
├── token_counter/
│   ├── __init__.py
│   ├── local_counter.py        # 本地Token计数器
│   └── remote_counter.py       # 远程Token计数器
├── calculator/
│   ├── __init__.py
│   └── token_cost_calculator.py # 成本计算器
├── config/
│   ├── __init__.py
│   └── model_pricing.py        # 模型价格配置
├── interceptors/
│   ├── __init__.py
│   └── token_interceptor.py    # LLM拦截器
├── display/
│   ├── __init__.py
│   └── console_printer.py      # 控制台输出
├── init/
│   ├── __init__.py
│   └── system_init.py          # 系统初始化
├── example/
│   ├── __init__.py
│   └── usage_example.py        # 使用示例
├── requirements.txt            # 依赖包
└── README.md                   # 项目说明
```

## 附录B：依赖包配置

```txt
# requirements.txt
pydantic>=2.0.0
sqlmodel>=0.0.8
tokenizers>=0.13.0
rich>=13.0.0
loguru>=0.7.0
```

## 附录C：高级功能扩展

### C.1 异步Token计数器

```python
# token_counter/async_counter.py
import asyncio
from typing import List
from loguru import logger

class AsyncTokenCounter:
    def __init__(self, tokenizer):
        self.tokenizer = tokenizer

    async def count_tokens_batch(self, texts: List[str]) -> List[int]:
        """批量异步计数Token"""
        tasks = [self.count_tokens_async(text) for text in texts]
        return await asyncio.gather(*tasks)

    async def count_tokens_async(self, text: str) -> int:
        """异步计数单个文本的Token"""
        try:
            # 这里可以是异步的tokenizer调用
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None, self._count_tokens_sync, text
            )
            return result
        except Exception as e:
            logger.error(f"Async token counting error: {str(e)}")
            return -1

    def _count_tokens_sync(self, text: str) -> int:
        """同步Token计数的内部方法"""
        encoded = self.tokenizer.encode(
            '{"role":"user","content":"' + text + '"}')
        return len(encoded.ids)
```

### C.2 Token使用分析器

```python
# analyzer/token_analyzer.py
from typing import Dict, List, Tuple
from datetime import datetime, timedelta
from models.database import Store, TokenCounter
from models.token_usage import TokenUsageStats

class TokenUsageAnalyzer:
    def __init__(self, store: Store):
        self.store = store

    def get_daily_usage(self, days: int = 7) -> Dict[str, TokenUsageStats]:
        """获取每日Token使用统计"""
        # 这里需要扩展数据库模型来包含时间戳
        # 简化示例
        pass

    def get_model_usage_comparison(self) -> Dict[str, TokenUsageStats]:
        """获取不同模型的使用对比"""
        pass

    def calculate_cost_trends(self, days: int = 30) -> List[Tuple[datetime, float]]:
        """计算成本趋势"""
        pass

    def generate_usage_report(self) -> str:
        """生成使用报告"""
        report = []
        report.append("=== Token使用报告 ===")
        report.append(f"生成时间: {datetime.now()}")

        # 添加各种统计信息
        counter = self.store.get_token_counter()
        if counter:
            total_tokens = counter.input_tokens_count + counter.generated_tokens_count
            report.append(f"总Token使用: {total_tokens}")
            report.append(f"输入Token: {counter.input_tokens_count}")
            report.append(f"输出Token: {counter.generated_tokens_count}")

        return "\n".join(report)
```

### C.3 Token预算管理器

```python
# budget/budget_manager.py
from typing import Optional
from datetime import datetime, timedelta
from models.token_usage import TokenUsageStats

class TokenBudgetManager:
    def __init__(self, daily_budget: float = 10.0, monthly_budget: float = 300.0):
        self.daily_budget = daily_budget
        self.monthly_budget = monthly_budget
        self.current_daily_usage = 0.0
        self.current_monthly_usage = 0.0

    def check_budget_before_request(self, estimated_cost: float) -> Tuple[bool, str]:
        """在请求前检查预算"""
        if self.current_daily_usage + estimated_cost > self.daily_budget:
            return False, f"超出每日预算限制 ${self.daily_budget}"

        if self.current_monthly_usage + estimated_cost > self.monthly_budget:
            return False, f"超出每月预算限制 ${self.monthly_budget}"

        return True, "预算检查通过"

    def update_usage(self, cost: float):
        """更新使用量"""
        self.current_daily_usage += cost
        self.current_monthly_usage += cost

    def get_remaining_budget(self) -> Dict[str, float]:
        """获取剩余预算"""
        return {
            "daily_remaining": self.daily_budget - self.current_daily_usage,
            "monthly_remaining": self.monthly_budget - self.current_monthly_usage
        }
```

## 附录D：测试用例

### D.1 单元测试示例

```python
# tests/test_token_calculator.py
import unittest
from calculator.token_cost_calculator import TokenCostCalculator
from models.token_usage import TokenUsageStats

class TestTokenCostCalculator(unittest.TestCase):
    def setUp(self):
        self.calculator = TokenCostCalculator()

    def test_calculate_token_costs(self):
        """测试Token成本计算"""
        model_info_map = {
            "gpt-4": {
                "input_price": 30.0,
                "output_price": 60.0
            }
        }

        stats = self.calculator.calculate_token_costs(
            input_tokens=1000,
            output_tokens=500,
            model_info_map=model_info_map
        )

        self.assertEqual(stats.input_tokens, 1000)
        self.assertEqual(stats.output_tokens, 500)
        self.assertEqual(stats.total_tokens, 1500)

        # 验证成本计算: (1000 * 30 + 500 * 60) / 1000000
        expected_cost = (1000 * 30.0 + 500 * 60.0) / 1000000
        self.assertAlmostEqual(stats.total_cost, expected_cost, places=6)

    def test_token_usage_stats_properties(self):
        """测试TokenUsageStats属性"""
        stats = TokenUsageStats(
            input_tokens=100,
            output_tokens=50,
            input_cost=0.001,
            output_cost=0.002
        )

        self.assertEqual(stats.total_tokens, 150)
        self.assertEqual(stats.total_cost, 0.003)

if __name__ == '__main__':
    unittest.main()
```

### D.2 集成测试示例

```python
# tests/test_integration.py
import unittest
import tempfile
import os
from models.database import Store
from calculator.token_cost_calculator import TokenCostCalculator

class TestIntegration(unittest.TestCase):
    def setUp(self):
        # 创建临时数据库
        self.temp_db = tempfile.NamedTemporaryFile(delete=False, suffix='.db')
        self.temp_db.close()
        self.store = Store(self.temp_db.name)
        self.calculator = TokenCostCalculator()

    def tearDown(self):
        # 清理临时文件
        os.unlink(self.temp_db.name)

    def test_full_workflow(self):
        """测试完整工作流程"""
        # 1. 更新Token计数器
        self.store.update_token_counter(
            project="test_project",
            input_tokens_count=1000,
            generated_tokens_count=500
        )

        # 2. 获取计数器数据
        counter = self.store.get_token_counter("test_project")
        self.assertIsNotNone(counter)
        self.assertEqual(counter.input_tokens_count, 1000)
        self.assertEqual(counter.generated_tokens_count, 500)

        # 3. 计算成本
        stats = self.calculator.track_token_usage(
            model_name="gpt-4",
            input_tokens=1000,
            output_tokens=500,
            operation_name="测试操作",
            start_time=0.0,
            end_time=1.0
        )

        self.assertEqual(stats.input_tokens, 1000)
        self.assertEqual(stats.output_tokens, 500)
        self.assertGreater(stats.total_cost, 0)

if __name__ == '__main__':
    unittest.main()
```

## 附录E：部署和维护

### E.1 Docker部署配置

```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

# 创建数据目录
RUN mkdir -p /app/data

# 设置环境变量
ENV TOKEN_DB_PATH=/app/data/token_usage.db
ENV TOKENIZER_PATH=/app/data/tokenizer.json

EXPOSE 8000

CMD ["python", "-m", "example.usage_example"]
```

### E.2 配置文件管理

```python
# config/settings.py
import os
from typing import Optional

class Settings:
    # 数据库配置
    DB_PATH: str = os.getenv("TOKEN_DB_PATH", "token_usage.db")

    # Tokenizer配置
    TOKENIZER_PATH: Optional[str] = os.getenv("TOKENIZER_PATH")

    # 预算配置
    DAILY_BUDGET: float = float(os.getenv("DAILY_BUDGET", "10.0"))
    MONTHLY_BUDGET: float = float(os.getenv("MONTHLY_BUDGET", "300.0"))

    # 日志配置
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE: Optional[str] = os.getenv("LOG_FILE")

    # 产品模式
    PRODUCT_MODE: str = os.getenv("PRODUCT_MODE", "lite")

settings = Settings()
```

### E.3 监控和告警

```python
# monitoring/alerts.py
from typing import Callable, List
from models.token_usage import TokenUsageStats

class AlertManager:
    def __init__(self):
        self.alert_handlers: List[Callable] = []

    def add_alert_handler(self, handler: Callable):
        """添加告警处理器"""
        self.alert_handlers.append(handler)

    def check_usage_alerts(self, stats: TokenUsageStats, thresholds: dict):
        """检查使用量告警"""
        alerts = []

        if stats.total_cost > thresholds.get("cost_threshold", 1.0):
            alerts.append(f"成本超过阈值: ${stats.total_cost:.6f}")

        if stats.total_tokens > thresholds.get("token_threshold", 10000):
            alerts.append(f"Token使用超过阈值: {stats.total_tokens}")

        # 触发告警
        for alert in alerts:
            for handler in self.alert_handlers:
                handler(alert)

def email_alert_handler(message: str):
    """邮件告警处理器"""
    # 实现邮件发送逻辑
    print(f"邮件告警: {message}")

def slack_alert_handler(message: str):
    """Slack告警处理器"""
    # 实现Slack消息发送逻辑
    print(f"Slack告警: {message}")
```

这个完整的迁移指南现在包含了：

1. **核心系统架构**：数据模型、计数器、成本计算
2. **高级功能**：异步处理、分析器、预算管理
3. **测试框架**：单元测试和集成测试
4. **部署配置**：Docker、配置管理、监控告警
5. **完整的项目结构**和使用示例

您可以根据这个指南逐步实现Token计费统计系统，每个阶段都有详细的代码示例和说明。
