<template>
  <div class="chat-view">
    <div class="chat-header">
      <h1>AI智能对话系统 - Phase 5验证</h1>
      <div class="header-actions">
        <router-link to="/test-composables" class="btn btn-secondary">
          返回测试页面
        </router-link>
        <router-link to="/" class="btn btn-secondary">
          返回首页
        </router-link>
      </div>
    </div>

    <div class="chat-container">
      <!-- 左侧：模型选择和状态 -->
      <div class="chat-sidebar">
        <div class="model-selection-section">
          <h3>🤖 模型选择</h3>
          <div class="model-selector">
            <select
              v-model="selectedModelId"
              @change="handleModelSelect"
              class="model-select"
              :disabled="models.isLoading"
            >
              <option value="">请选择聊天模型</option>
              <option 
                v-for="model in activeModels" 
                :key="model.id" 
                :value="model.id"
              >
                {{ model.display_name || model.name }}
              </option>
            </select>
            <button @click="refreshModels" class="btn btn-small" :disabled="models.isLoading">
              {{ models.isLoading ? '加载中...' : '刷新模型' }}
            </button>
          </div>

          <!-- 选中模型信息显示 -->
          <div v-if="selectedModel" class="selected-model-info">
            <h4>当前模型信息</h4>
            <div class="model-details">
              <p><strong>名称:</strong> {{ selectedModel.display_name || selectedModel.name }}</p>
              <p v-if="selectedModel.description"><strong>描述:</strong> {{ selectedModel.description }}</p>
              <p v-if="selectedModel.capabilities && selectedModel.capabilities.length">
                <strong>能力:</strong> {{ selectedModel.capabilities.join(', ') }}
              </p>
              <p><strong>支持推理:</strong> {{ supportsReasoning ? '是' : '否' }}</p>
            </div>
          </div>

          <!-- 连接状态 -->
          <div class="connection-status">
            <h4>连接状态</h4>
            <div class="status-indicator" :class="connectionStatus">
              <span class="status-dot"></span>
              <span class="status-text">{{ getConnectionStatusText() }}</span>
            </div>
          </div>
        </div>

        <!-- 对话列表 -->
        <div class="conversations-section">
          <div class="conversations-header">
            <h3>💬 对话列表</h3>
            <button @click="refreshConversations" class="btn btn-small">刷新</button>
          </div>
          <div class="conversations-list">
            <div v-if="conversations.length === 0" class="empty-conversations">
              暂无对话记录
            </div>
            <div 
              v-for="conversation in conversations.slice(0, 5)" 
              :key="conversation.id"
              class="conversation-item"
              :class="{ active: conversation.id === currentConversation?.id }"
              @click="switchToConversation(conversation.id)"
            >
              <div class="conversation-title">{{ conversation.title || '未命名对话' }}</div>
              <div class="conversation-preview">{{ conversation.last_message || '暂无消息' }}</div>
              <div class="conversation-time">{{ formatTime(conversation.updated_at) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：聊天区域 -->
      <div class="chat-main">
        <!-- 消息显示区域 -->
        <div class="messages-container" ref="messagesContainer">
          <div v-if="currentMessages.length === 0" class="empty-messages">
            <p>开始新的对话吧！</p>
            <p class="text-secondary">请先选择一个AI模型，然后输入您的问题。</p>
          </div>

          <!-- 历史消息 -->
          <div 
            v-for="message in currentMessages" 
            :key="message.id"
            class="message-item"
            :class="message.role"
          >
            <div class="message-header">
              <span class="message-role">
                {{ message.role === 'user' ? '👤 您' : '🤖 AI' }}
              </span>
              <span class="message-time">{{ formatTime(message.created_at) }}</span>
            </div>

            <!-- AI思考过程 -->
            <div v-if="message.role === 'assistant' && message.thinking" class="thinking-content">
              <div class="thinking-header">
                <span class="thinking-icon">🤔</span>
                <strong>思考过程:</strong>
              </div>
              <div class="thinking-text">{{ message.thinking }}</div>
            </div>

            <!-- 消息内容 -->
            <div class="message-content">{{ message.content }}</div>
          </div>

          <!-- 当前流式消息 -->
          <div v-if="streamingMessage" class="message-item streaming">
            <div class="message-header">
              <span class="message-role">👤 您</span>
              <span class="message-time">刚刚</span>
            </div>
            <div class="message-content">{{ streamingMessage.userMessage }}</div>
          </div>

          <div v-if="streamingMessage" class="message-item assistant streaming">
            <div class="message-header">
              <span class="message-role">🤖 AI</span>
              <span class="message-time">
                {{ streamingMessage.isComplete ? '刚刚' : '正在回复...' }}
              </span>
            </div>

            <!-- 思考过程 -->
            <div v-if="streamingMessage.thinking || streamingMessage.isThinking" class="thinking-content">
              <div class="thinking-header">
                <span class="thinking-icon">🤔</span>
                <strong>思考过程:</strong>
                <span v-if="streamingMessage.isThinking" class="thinking-indicator">思考中...</span>
              </div>
              <div class="thinking-text">{{ streamingMessage.thinking }}</div>
            </div>

            <!-- AI回复内容 -->
            <div class="message-content">{{ streamingMessage.content }}</div>

            <!-- 状态指示器 -->
            <div class="message-status">
              <span v-if="streamingMessage.isComplete" class="complete-indicator">✅ 回复完成</span>
              <span v-else-if="streamingMessage.content" class="streaming-indicator">⏳ 正在生成...</span>
              <span v-else-if="streamingMessage.isThinking" class="thinking-indicator">🤔 正在思考...</span>
            </div>
          </div>
        </div>

        <!-- 消息输入区域 -->
        <div class="message-input-area">
          <div class="input-container">
            <textarea
              v-model="messageInput"
              @keydown="handleKeyDown"
              placeholder="输入您的消息... (Shift+Enter换行，Enter发送)"
              class="message-input"
              :disabled="!selectedModelId || isSending"
              rows="3"
            ></textarea>
            <div class="input-actions">
              <button 
                @click="sendMessage"
                class="btn btn-primary"
                :disabled="!canSendMessage"
                :class="{ loading: isSending }"
              >
                {{ isSending ? '发送中...' : '发送消息' }}
              </button>
              <button 
                @click="sendStreamMessage"
                class="btn btn-secondary"
                :disabled="!canSendMessage"
                :class="{ loading: isSending }"
              >
                {{ isSending ? '发送中...' : '流式发送' }}
              </button>
              <button @click="createNewConversation" class="btn btn-outline">
                新建对话
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 测试结果显示区域 -->
    <div class="test-results-section">
      <div class="results-header">
        <h3>📋 测试结果</h3>
        <button @click="clearTestResults" class="btn btn-small">清除日志</button>
      </div>
      <div class="results-content">
        <div v-for="result in testResults" :key="result.id" class="result-item" :class="result.type">
          <span class="timestamp">{{ formatTime(result.timestamp) }}</span>
          <span class="message">{{ result.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useChat, useModels } from '@/composables'

// 初始化composables
const chat = useChat()
const models = useModels()

// 本地状态
const messageInput = ref('')
const messagesContainer = ref<HTMLElement>()
const testResults = ref<Array<{
  id: string
  type: 'success' | 'error' | 'info'
  message: string
  timestamp: Date
}>>([])

// 流式消息状态
const streamingMessage = ref<{
  userMessage: string
  content: string
  thinking: string
  isThinking: boolean
  isComplete: boolean
} | null>(null)

// 计算属性
const activeModels = computed(() =>
  models.availableModels.value.filter(model => model.status === 'active' || !model.status)
)

const selectedModel = computed(() => models.selectedModel.value)
const selectedModelId = computed({
  get: () => models.selectedModelId.value,
  set: (value: string) => {
    if (value) {
      models.selectModel(value)
    }
  }
})

const supportsReasoning = computed(() => 
  selectedModel.value?.capabilities?.includes('reasoning') || false
)

const conversations = computed(() => chat.conversations.value || [])
const currentConversation = computed(() => chat.currentConversation.value)
const currentMessages = computed(() => chat.currentMessages.value || [])
const connectionStatus = computed(() => chat.connectionStatus.value)
const isSending = computed(() => chat.isSending.value)

const canSendMessage = computed(() => 
  selectedModelId.value && 
  messageInput.value.trim() && 
  !isSending.value
)

// 计数器确保唯一ID
let resultCounter = 0

// 方法
const addTestResult = (type: 'success' | 'error' | 'info', message: string) => {
  resultCounter++
  testResults.value.unshift({
    id: `result-${resultCounter}-${Date.now()}`,
    type,
    message,
    timestamp: new Date()
  })

  // 限制结果数量
  if (testResults.value.length > 50) {
    testResults.value = testResults.value.slice(0, 50)
  }
}

const clearTestResults = () => {
  testResults.value = []
  addTestResult('info', '测试日志已清除')
}

const formatTime = (dateString: string | Date): string => {
  if (!dateString) return '未知时间'
  
  try {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    
    if (diffMins < 1) return '刚刚'
    if (diffMins < 60) return `${diffMins}分钟前`
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}小时前`
    
    return date.toLocaleDateString()
  } catch (error) {
    return '时间格式错误'
  }
}

const getConnectionStatusText = (): string => {
  switch (connectionStatus.value) {
    case 'connected': return '已连接'
    case 'connecting': return '连接中'
    case 'disconnected': return '未连接'
    default: return '未知状态'
  }
}

const handleModelSelect = () => {
  const model = selectedModel.value
  if (model) {
    addTestResult('success', `已选择模型: ${model.display_name || model.name}`)
    if (model.description) {
      addTestResult('info', `模型描述: ${model.description}`)
    }
    if (model.capabilities) {
      addTestResult('info', `模型能力: ${model.capabilities.join(', ')}`)
    }
  }
}

const refreshModels = async () => {
  try {
    addTestResult('info', '正在刷新模型列表...')
    await models.fetchModels()
    addTestResult('success', `模型列表已刷新，共 ${models.availableModels.value.length} 个模型`)
  } catch (error) {
    addTestResult('error', `刷新模型列表失败: ${error}`)
  }
}

const refreshConversations = async () => {
  try {
    addTestResult('info', '正在刷新对话列表...')
    await chat.refreshConversations()
    addTestResult('success', `对话列表已刷新，共 ${conversations.value.length} 个对话`)
  } catch (error) {
    addTestResult('error', `刷新对话列表失败: ${error}`)
  }
}

const switchToConversation = async (conversationId: string) => {
  try {
    addTestResult('info', `正在切换到对话: ${conversationId}`)
    await chat.switchConversation(conversationId)
    addTestResult('success', '对话切换成功')
    scrollToBottom()
  } catch (error) {
    addTestResult('error', `切换对话失败: ${error}`)
  }
}

const createNewConversation = async () => {
  try {
    if (!chat.ensureModelSelected()) {
      addTestResult('error', '请先选择一个聊天模型')
      return
    }
    
    addTestResult('info', '正在创建新对话...')
    const conversationId = await chat.createConversation('新对话')
    
    if (conversationId) {
      addTestResult('success', `新对话创建成功: ${conversationId}`)
      messageInput.value = ''
      scrollToBottom()
    } else {
      addTestResult('error', '创建新对话失败')
    }
  } catch (error) {
    addTestResult('error', `创建新对话失败: ${error}`)
  }
}

const sendMessage = async () => {
  if (!canSendMessage.value) return
  
  try {
    const content = messageInput.value.trim()
    addTestResult('info', `发送普通消息: "${content}"`)
    
    const message = await chat.sendMessage(content)
    
    if (message) {
      addTestResult('success', `消息发送成功: ${message.id}`)
      messageInput.value = ''
      scrollToBottom()
    } else {
      addTestResult('error', '消息发送失败')
    }
  } catch (error) {
    addTestResult('error', `发送消息失败: ${error}`)
  }
}

const sendStreamMessage = async () => {
  if (!canSendMessage.value) return
  
  try {
    const content = messageInput.value.trim()
    addTestResult('info', `发送流式消息: "${content}"`)
    
    // 初始化流式消息状态
    streamingMessage.value = {
      userMessage: content,
      content: '',
      thinking: '',
      isThinking: false,
      isComplete: false
    }
    
    const message = await chat.sendStreamMessage(
      content,
      currentConversation.value?.id,
      (chunk) => {
        if (!streamingMessage.value) return
        
        switch (chunk.type) {
          case 'thinking_mode':
            streamingMessage.value.isThinking = chunk.isThinking
            if (chunk.isThinking) {
              addTestResult('info', '🤔 AI开始思考...')
            }
            break
            
          case 'thinking_chunk':
            streamingMessage.value.thinking += chunk.content
            break
            
          case 'thinking_complete':
            streamingMessage.value.thinking = chunk.content
            streamingMessage.value.isThinking = false
            addTestResult('info', '🧠 思考完成，开始生成回复')
            break
            
          case 'content':
            streamingMessage.value.content += chunk.content
            scrollToBottom()
            break
            
          case 'complete':
            streamingMessage.value.isComplete = true
            streamingMessage.value.content = chunk.message.content
            if (chunk.message.thinking) {
              streamingMessage.value.thinking = chunk.message.thinking
            }
            addTestResult('success', '✅ 流式消息完成')
            
            // 清除流式状态并刷新对话
            setTimeout(() => {
              streamingMessage.value = null
              chat.refreshConversations()
            }, 1000)
            break
        }
      }
    )
    
    if (message) {
      addTestResult('success', `流式消息发送成功: ${message.id}`)
      messageInput.value = ''
    } else {
      addTestResult('error', '流式消息发送失败')
      streamingMessage.value = null
    }
    
  } catch (error) {
    addTestResult('error', `发送流式消息失败: ${error}`)
    streamingMessage.value = null
  }
}

const handleKeyDown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendStreamMessage()
  }
}

const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 初始化
onMounted(async () => {
  addTestResult('info', 'Phase 5 聊天界面已加载')
  
  try {
    // 获取模型列表
    addTestResult('info', '正在获取模型列表...')
    await models.fetchModels()
    addTestResult('success', `模型列表已加载，共 ${models.availableModels.value.length} 个模型`)
    
    // 自动选择第一个活跃模型
    if (activeModels.value.length > 0 && !selectedModel.value) {
      const firstModel = activeModels.value[0]
      models.selectModel(firstModel.id)
      addTestResult('info', `自动选择第一个活跃模型: ${firstModel.display_name || firstModel.name}`)
    }
    
    // 获取对话列表
    addTestResult('info', '正在获取对话列表...')
    await chat.refreshConversations()
    addTestResult('success', `对话列表已加载，共 ${conversations.value.length} 个对话`)
    
  } catch (error) {
    addTestResult('error', `初始化失败: ${error}`)
  }
})
</script>

<style scoped>
.chat-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary, #f8fafc);
}

.chat-header {
  background: white;
  padding: 1rem 2rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.chat-header h1 {
  margin: 0;
  color: #1a202c;
  font-size: 1.5rem;
}

.header-actions {
  display: flex;
  gap: 0.5rem;
}

.chat-container {
  display: flex;
  flex: 1;
  min-height: 0;
}

.chat-sidebar {
  width: 350px;
  background: white;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.model-selection-section,
.conversations-section {
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.model-selection-section h3,
.conversations-section h3 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1rem;
}

.model-selector {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.model-select {
  padding: 0.5rem;
  border: 1px solid #cbd5e0;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

.selected-model-info {
  margin-top: 1rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 0.375rem;
  border-left: 4px solid #3182ce;
}

.selected-model-info h4 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 0.875rem;
}

.model-details p {
  margin: 0.25rem 0;
  font-size: 0.75rem;
  color: #4a5568;
}

.connection-status {
  margin-top: 1rem;
}

.connection-status h4 {
  margin: 0 0 0.5rem 0;
  color: #2d3748;
  font-size: 0.875rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  border-radius: 0.375rem;
}

.status-indicator.connected {
  background: #f0fff4;
  color: #22543d;
}

.status-indicator.connecting {
  background: #fffbf0;
  color: #744210;
}

.status-indicator.disconnected {
  background: #fff5f5;
  color: #742a2a;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.connected .status-dot {
  background: #48bb78;
}

.connecting .status-dot {
  background: #ed8936;
}

.disconnected .status-dot {
  background: #f56565;
}

.conversations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.conversations-list {
  max-height: 300px;
  overflow-y: auto;
}

.empty-conversations {
  text-align: center;
  color: #a0aec0;
  font-size: 0.875rem;
  padding: 2rem 1rem;
}

.conversation-item {
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
}

.conversation-item:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.conversation-item.active {
  background: #ebf8ff;
  border-color: #3182ce;
}

.conversation-title {
  font-weight: 500;
  color: #2d3748;
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.conversation-preview {
  color: #718096;
  font-size: 0.75rem;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.conversation-time {
  color: #a0aec0;
  font-size: 0.625rem;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 1rem;
  background: white;
}

.empty-messages {
  text-align: center;
  color: #a0aec0;
  padding: 4rem 2rem;
}

.empty-messages .text-secondary {
  color: #cbd5e0;
  font-size: 0.875rem;
  margin-top: 0.5rem;
}

.message-item {
  margin-bottom: 1.5rem;
  max-width: 80%;
}

.message-item.user {
  margin-left: auto;
}

.message-item.assistant {
  margin-right: auto;
}

.message-item.streaming {
  opacity: 0.8;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  font-size: 0.75rem;
  color: #718096;
}

.message-role {
  font-weight: 500;
}

.thinking-content {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
}

.thinking-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: #4a5568;
}

.thinking-icon {
  font-size: 1rem;
}

.thinking-indicator {
  color: #ed8936;
  font-size: 0.75rem;
  font-style: italic;
}

.thinking-text {
  color: #2d3748;
  font-size: 0.875rem;
  line-height: 1.4;
  white-space: pre-wrap;
}

.message-content {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  padding: 0.75rem;
  color: #2d3748;
  line-height: 1.5;
  white-space: pre-wrap;
}

.message-item.user .message-content {
  background: #3182ce;
  color: white;
  border-color: #2c5282;
}

.message-status {
  margin-top: 0.5rem;
  font-size: 0.75rem;
}

.complete-indicator {
  color: #38a169;
}

.streaming-indicator {
  color: #ed8936;
}

.message-input-area {
  background: white;
  border-top: 1px solid #e2e8f0;
  padding: 1rem;
  flex-shrink: 0;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.message-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #cbd5e0;
  border-radius: 0.375rem;
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
  font-size: 0.875rem;
}

.message-input:focus {
  outline: none;
  border-color: #3182ce;
  box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
}

.message-input:disabled {
  background: #f7fafc;
  color: #a0aec0;
}

.input-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.test-results-section {
  background: white;
  border-top: 1px solid #e2e8f0;
  height: 200px;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.results-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f7fafc;
}

.results-header h3 {
  margin: 0;
  color: #2d3748;
  font-size: 0.875rem;
}

.results-content {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem;
  font-family: 'Courier New', monospace;
  font-size: 0.75rem;
}

.result-item {
  display: flex;
  gap: 0.5rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  margin-bottom: 0.25rem;
}

.result-item.success {
  background: #f0fff4;
  color: #22543d;
}

.result-item.error {
  background: #fff5f5;
  color: #742a2a;
}

.result-item.info {
  background: #ebf8ff;
  color: #2a4365;
}

.timestamp {
  color: #718096;
  white-space: nowrap;
}

.btn {
  padding: 0.5rem 1rem;
  border: 1px solid #cbd5e0;
  border-radius: 0.375rem;
  background: white;
  color: #4a5568;
  cursor: pointer;
  font-size: 0.875rem;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  transition: all 0.2s;
}

.btn:hover {
  background: #f7fafc;
  border-color: #a0aec0;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn.loading {
  opacity: 0.7;
}

.btn-primary {
  background: #3182ce;
  color: white;
  border-color: #2c5282;
}

.btn-primary:hover:not(:disabled) {
  background: #2c5282;
}

.btn-secondary {
  background: #718096;
  color: white;
  border-color: #4a5568;
}

.btn-secondary:hover:not(:disabled) {
  background: #4a5568;
}

.btn-outline {
  background: transparent;
  color: #3182ce;
  border-color: #3182ce;
}

.btn-outline:hover:not(:disabled) {
  background: #ebf8ff;
}

.btn-small {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}
</style>
