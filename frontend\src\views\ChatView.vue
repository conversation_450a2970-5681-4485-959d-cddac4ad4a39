<template>
  <div class="chat-view">
    <!-- 顶部导航栏 -->
    <div class="chat-header">
      <div class="header-left">
        <h1 class="chat-title">AI智能对话</h1>
        <div class="connection-status" :class="chat.connectionStatus?.value">
          <span class="status-indicator"></span>
          <span class="status-text">{{ getConnectionStatusText() }}</span>
        </div>
      </div>
      
      <div class="header-right">
        <div class="model-info" v-if="selectedModel">
          <span class="model-name">{{ selectedModel.display_name || selectedModel.name }}</span>
          <span class="model-description" v-if="selectedModel.description">
            {{ selectedModel.description }}
          </span>
        </div>
        <el-button @click="showModelSelector = true" type="primary" size="small">
          选择模型
        </el-button>
        <el-button @click="showConversationList = !showConversationList" size="small">
          对话列表
        </el-button>
      </div>
    </div>

    <div class="chat-container">
      <!-- 左侧对话列表 -->
      <div class="conversation-sidebar" v-show="showConversationList">
        <div class="sidebar-header">
          <h3>对话历史</h3>
          <el-button @click="createNewConversation" type="primary" size="small">
            新对话
          </el-button>
        </div>
        
        <div class="conversation-list">
          <div v-if="conversationsLength === 0" class="empty-state">
            <p>暂无对话记录</p>
            <p class="text-secondary">创建第一个对话开始聊天吧！</p>
          </div>

          <div v-else class="conversation-items">
            <div
              v-for="conversation in sortedConversationsValue"
              :key="conversation.id"
              class="conversation-item"
              :class="{
                'active': conversation.id === chat.currentConversation?.value?.id,
                'archived': conversation.is_archived
              }"
              @click="switchToConversation(conversation.id)"
            >
              <div class="conversation-item-header">
                <h5 class="conversation-title">{{ conversation.title || '未命名对话' }}</h5>
                <span class="conversation-time">{{ formatMessageTime(conversation.updated_at) }}</span>
              </div>

              <div class="conversation-item-content">
                <p class="last-message">{{ conversation.last_message || '暂无消息' }}</p>
                <div class="conversation-meta">
                  <span class="message-count">{{ conversation.message_count || 0 }} 条消息</span>
                  <span v-if="conversation.model_name" class="model-name">{{ conversation.model_name }}</span>
                </div>
              </div>

              <div class="conversation-actions">
                <el-button @click.stop="deleteConversationFromList(conversation.id)" 
                          type="danger" 
                          size="small" 
                          text>
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主聊天区域 -->
      <div class="chat-main">
        <!-- 消息显示区域 -->
        <div class="messages-container" ref="messagesContainer">
          <!-- 历史消息 -->
          <div
            v-for="(message, index) in conversationMessages"
            :key="message.id || index"
            class="message-item"
            :class="message.role"
          >
            <div class="message-header">
              <span class="message-role">
                {{ message.role === 'user' ? '👤 用户' : '🤖 AI' }}
              </span>
              <span class="message-time">
                {{ formatMessageTime(message.created_at) }}
              </span>
            </div>

            <!-- Thinking内容（仅AI消息且有thinking时显示） -->
            <div v-if="message.role === 'assistant' && message.thinking" class="thinking-content">
              <div class="thinking-header">
                <span class="thinking-icon">🤔</span>
                <strong>思考过程:</strong>
              </div>
              <div class="thinking-text">{{ message.thinking }}</div>
            </div>

            <!-- 消息内容 -->
            <div class="message-content">{{ message.content }}</div>
          </div>

          <!-- 当前流式消息（如果正在发送） -->
          <div v-if="streamingMessage" class="message-item user">
            <div class="message-header">
              <span class="message-role">👤 用户</span>
              <span class="message-time">刚刚</span>
            </div>
            <div class="message-content">{{ streamingMessage.userMessage }}</div>
          </div>

          <div v-if="streamingMessage" class="message-item assistant streaming">
            <div class="message-header">
              <span class="message-role">🤖 AI</span>
              <span class="message-time">
                {{ streamingMessage.isComplete ? '刚刚' : '正在回复...' }}
              </span>
            </div>

            <!-- 当前thinking内容 -->
            <div v-if="streamingMessage.thinking || streamingMessage.isThinking" class="thinking-content">
              <div class="thinking-header">
                <span class="thinking-icon">🤔</span>
                <strong>思考过程:</strong>
                <span v-if="streamingMessage.isThinking" class="thinking-indicator">思考中...</span>
              </div>
              <div class="thinking-text">{{ streamingMessage.thinking }}</div>
            </div>

            <!-- 当前AI回复内容 -->
            <div class="message-content">{{ streamingMessage.content }}</div>

            <!-- 状态指示器 -->
            <div class="message-status">
              <span v-if="streamingMessage.isComplete" class="complete-indicator">✅ 回复完成</span>
              <span v-else-if="streamingMessage.content" class="streaming-indicator">⏳ 正在生成...</span>
              <span v-else-if="streamingMessage.isThinking" class="thinking-indicator">🤔 正在思考...</span>
            </div>
          </div>
        </div>

        <!-- 消息输入区域 -->
        <div class="message-input-container">
          <div class="input-area">
            <el-input
              v-model="chatMessageInput"
              type="textarea"
              placeholder="输入消息..."
              :disabled="!selectedModel || chat.isSending"
              @keydown="handleKeydown"
              :autosize="{ minRows: 1, maxRows: 4 }"
              resize="none"
            />
            <el-button
              type="primary"
              :disabled="!canSendMessage"
              :loading="chat.isSending"
              @click="sendMessage"
              class="send-button"
            >
              发送
            </el-button>
          </div>
          
          <div class="input-status" v-if="!selectedModel">
            <span class="warning-text">请先选择一个AI模型开始对话</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 模型选择弹窗 -->
    <el-dialog v-model="showModelSelector" title="选择AI模型" width="600px">
      <div class="model-selector">
        <div class="model-grid">
          <div
            v-for="model in activeModelsValue"
            :key="model.id"
            class="model-card"
            :class="{ 'selected': model.id === models.selectedModelId?.value }"
            @click="selectModel(model.id)"
          >
            <div class="model-header">
              <h4>{{ model.display_name || model.name }}</h4>
              <span class="model-provider">{{ model.provider || '未知' }}</span>
            </div>
            <div class="model-description">
              {{ model.description || '暂无描述' }}
            </div>
            <div class="model-capabilities" v-if="model.capabilities">
              <span v-for="capability in model.capabilities" :key="capability" class="capability-tag">
                {{ capability }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showModelSelector = false">取消</el-button>
        <el-button type="primary" @click="confirmModelSelection">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, nextTick } from 'vue'
import { useChat, useModels } from '@/composables'
import { ElMessage } from 'element-plus'

// 初始化composables
const chat = useChat()
const models = useModels()

// 本地状态
const showConversationList = ref(true)
const showModelSelector = ref(false)
const messagesContainer = ref<HTMLElement>()

// 流式消息状态
const streamingMessage = ref<{
  userMessage: string
  content: string
  thinking: string
  isThinking: boolean
  isComplete: boolean
} | null>(null)

// 计算属性
const conversationsLength = computed(() => chat.conversations?.value?.length || 0)
const selectedModel = computed(() => models.selectedModel?.value || null)
const activeModelsValue = computed(() => models.activeModels?.value || [])
const conversationMessages = computed(() => chat.currentMessages?.value || [])
const sortedConversationsValue = computed(() => chat.sortedConversations?.value || [])

const chatMessageInput = computed({
  get: () => chat.messageInput?.value || '',
  set: (value: string) => {
    if (chat.messageInput) {
      chat.messageInput.value = value
    }
  }
})

const canSendMessage = computed(() => {
  return selectedModel.value &&
         chatMessageInput.value.trim() &&
         !chat.isSending?.value
})

// 获取连接状态文本
const getConnectionStatusText = (): string => {
  switch (chat.connectionStatus?.value) {
    case 'connected': return '已连接'
    case 'connecting': return '连接中'
    case 'disconnected': return '未连接'
    default: return '未知状态'
  }
}

// 格式化消息时间
const formatMessageTime = (dateString: string): string => {
  if (!dateString) return '未知时间'

  try {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return '刚刚'
    if (diffMins < 60) return `${diffMins}分钟前`
    if (diffHours < 24) return `${diffHours}小时前`
    if (diffDays < 7) return `${diffDays}天前`

    return date.toLocaleDateString()
  } catch (error) {
    return '时间格式错误'
  }
}

// 选择模型
const selectModel = (modelId: string): void => {
  const success = models.selectModel(modelId)
  if (success) {
    const model = activeModelsValue.value.find(m => m.id === modelId)
    ElMessage.success(`已选择模型：${model?.display_name || model?.name || modelId}`)
  } else {
    ElMessage.error('选择模型失败')
  }
}

// 确认模型选择
const confirmModelSelection = (): void => {
  showModelSelector.value = false
  if (selectedModel.value) {
    ElMessage.success(`当前模型：${selectedModel.value.display_name || selectedModel.value.name}`)
  }
}

// 创建新对话
const createNewConversation = async (): Promise<void> => {
  try {
    if (!chat.ensureModelSelected()) {
      ElMessage.warning('请先选择一个AI模型')
      showModelSelector.value = true
      return
    }

    const id = await chat.createConversation('新对话')
    if (id) {
      ElMessage.success('创建对话成功')
      await chat.refreshConversations()
    }
  } catch (error) {
    ElMessage.error(`创建对话失败: ${error}`)
  }
}

// 切换到指定对话
const switchToConversation = async (conversationId: string): Promise<void> => {
  try {
    await chat.switchConversation(conversationId)
    ElMessage.success('切换对话成功')
    scrollToBottom()
  } catch (error) {
    ElMessage.error(`切换对话失败: ${error}`)
  }
}

// 删除对话
const deleteConversationFromList = async (conversationId: string): Promise<void> => {
  try {
    const conversation = chat.conversations?.value?.find(c => c.id === conversationId)
    const title = conversation?.title || '未命名对话'

    const success = await chat.deleteConversation(conversationId)
    if (success) {
      ElMessage.success(`对话 "${title}" 已删除`)
      await chat.refreshConversations()
    } else {
      ElMessage.error(`删除对话 "${title}" 失败`)
    }
  } catch (error) {
    ElMessage.error(`删除对话失败: ${error}`)
  }
}

// 发送消息
const sendMessage = async (): Promise<void> => {
  if (!canSendMessage.value) return

  try {
    if (!chat.ensureModelSelected()) {
      ElMessage.warning('请先选择一个AI模型')
      showModelSelector.value = true
      return
    }

    const messageContent = chatMessageInput.value.trim()
    if (!messageContent) return

    // 初始化流式消息状态
    streamingMessage.value = {
      userMessage: messageContent,
      content: '',
      thinking: '',
      isThinking: false,
      isComplete: false
    }

    // 清空输入框
    chatMessageInput.value = ''

    // 调用流式消息API
    const message = await chat.sendStreamMessage(
      messageContent,
      chat.currentConversation?.value?.id,
      (chunk) => {
        // 处理流式数据块
        if (!streamingMessage.value) return

        switch (chunk.type) {
          case 'thinking_mode':
            streamingMessage.value.isThinking = chunk.isThinking
            break

          case 'thinking_chunk':
            streamingMessage.value.thinking += chunk.content
            break

          case 'thinking_complete':
            streamingMessage.value.thinking = chunk.content
            streamingMessage.value.isThinking = false
            break

          case 'content':
            streamingMessage.value.content += chunk.content
            scrollToBottom()
            break

          case 'complete':
            streamingMessage.value.isComplete = true
            streamingMessage.value.content = chunk.message.content
            if (chunk.message.thinking) {
              streamingMessage.value.thinking = chunk.message.thinking
            }

            // 延迟清除流式消息状态并刷新对话
            setTimeout(() => {
              streamingMessage.value = null
              chat.refreshConversations()
              scrollToBottom()
            }, 500)
            break
        }
      }
    )

    if (!message) {
      streamingMessage.value = null
      ElMessage.error('发送消息失败')
    }

  } catch (error) {
    streamingMessage.value = null
    ElMessage.error(`发送消息失败: ${error}`)
  }
}

// 处理键盘事件
const handleKeydown = (event: KeyboardEvent): void => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault()
    sendMessage()
  }
}

// 滚动到底部
const scrollToBottom = (): void => {
  nextTick(() => {
    if (messagesContainer.value) {
      messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight
    }
  })
}

// 初始化
onMounted(async () => {
  try {
    // 获取模型列表
    await models.fetchModels()

    // 如果没有选中模型且有可用模型，提示用户选择
    if (!models.selectedModelId?.value && models.activeModels?.value && models.activeModels.value.length > 0) {
      ElMessage.info('请选择一个AI模型开始对话')
      showModelSelector.value = true
    }

    // 获取对话列表
    await chat.refreshConversations()

    // 滚动到底部
    scrollToBottom()
  } catch (error) {
    ElMessage.error(`初始化失败: ${error}`)
  }
})
</script>

<style scoped>
.chat-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary, #ffffff);
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background: var(--bg-secondary, #f9fafb);
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.chat-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.connection-status.connected {
  background: #dcfce7;
  color: #166534;
}

.connection-status.connecting {
  background: #fef3c7;
  color: #92400e;
}

.connection-status.disconnected {
  background: #fee2e2;
  color: #991b1b;
}

.status-indicator {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.model-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  max-width: 200px;
}

.model-name {
  font-weight: 500;
  color: var(--text-primary, #1f2937);
  font-size: 14px;
}

.model-description {
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
  text-align: right;
  line-height: 1.3;
}

.chat-container {
  display: flex;
  flex: 1;
  min-height: 0;
}

.conversation-sidebar {
  width: 300px;
  border-right: 1px solid var(--border-color, #e5e7eb);
  background: var(--bg-tertiary, #f9fafb);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-secondary, #ffffff);
}

.sidebar-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
}

.conversation-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary, #6b7280);
}

.conversation-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.conversation-item {
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
  background: var(--bg-secondary, #ffffff);
}

.conversation-item:hover {
  background: var(--bg-hover, #f3f4f6);
  border-color: var(--border-hover, #d1d5db);
}

.conversation-item.active {
  background: var(--primary-light, #dbeafe);
  border-color: var(--primary-color, #3b82f6);
}

.conversation-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.conversation-title {
  margin: 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary, #1f2937);
  line-height: 1.3;
  flex: 1;
}

.conversation-time {
  font-size: 11px;
  color: var(--text-secondary, #6b7280);
  white-space: nowrap;
  margin-left: 8px;
}

.conversation-item-content {
  margin-bottom: 8px;
}

.last-message {
  margin: 0 0 6px 0;
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.conversation-meta {
  display: flex;
  gap: 8px;
  font-size: 11px;
  color: var(--text-tertiary, #9ca3af);
}

.conversation-actions {
  display: flex;
  justify-content: flex-end;
}

.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-width: 80%;
}

.message-item.user {
  align-self: flex-end;
  align-items: flex-end;
}

.message-item.assistant {
  align-self: flex-start;
  align-items: flex-start;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
}

.message-role {
  font-weight: 500;
}

.thinking-content {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 8px;
}

.thinking-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #92400e;
}

.thinking-icon {
  font-size: 14px;
}

.thinking-indicator {
  font-size: 11px;
  opacity: 0.8;
}

.thinking-text {
  font-size: 13px;
  line-height: 1.4;
  color: #78350f;
  white-space: pre-wrap;
}

.message-content {
  background: var(--bg-message, #f3f4f6);
  padding: 12px 16px;
  border-radius: 12px;
  font-size: 14px;
  line-height: 1.5;
  color: var(--text-primary, #1f2937);
  white-space: pre-wrap;
  word-wrap: break-word;
}

.message-item.user .message-content {
  background: var(--primary-color, #3b82f6);
  color: white;
}

.message-item.assistant .message-content {
  background: var(--bg-secondary, #f9fafb);
  border: 1px solid var(--border-color, #e5e7eb);
}

.message-item.streaming .message-content {
  border-bottom: 2px solid var(--primary-color, #3b82f6);
  animation: pulse 1.5s ease-in-out infinite;
}

.message-status {
  font-size: 11px;
  color: var(--text-secondary, #6b7280);
  margin-top: 4px;
}

.complete-indicator {
  color: #059669;
}

.streaming-indicator {
  color: #f59e0b;
}

.thinking-indicator {
  color: #8b5cf6;
}

.message-input-container {
  padding: 16px;
  border-top: 1px solid var(--border-color, #e5e7eb);
  background: var(--bg-secondary, #f9fafb);
  flex-shrink: 0;
}

.input-area {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.input-area :deep(.el-textarea) {
  flex: 1;
}

.send-button {
  flex-shrink: 0;
}

.input-status {
  margin-top: 8px;
  text-align: center;
}

.warning-text {
  color: #f59e0b;
  font-size: 13px;
}

.model-selector {
  max-height: 400px;
  overflow-y: auto;
}

.model-grid {
  display: grid;
  gap: 12px;
}

.model-card {
  padding: 16px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  background: var(--bg-secondary, #ffffff);
}

.model-card:hover {
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.model-card.selected {
  border-color: var(--primary-color, #3b82f6);
  background: var(--primary-light, #dbeafe);
}

.model-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.model-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
}

.model-provider {
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
  background: var(--bg-tertiary, #f3f4f6);
  padding: 2px 6px;
  border-radius: 4px;
}

.model-description {
  font-size: 13px;
  color: var(--text-secondary, #6b7280);
  line-height: 1.4;
  margin-bottom: 8px;
}

.model-capabilities {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.capability-tag {
  font-size: 11px;
  background: var(--primary-light, #dbeafe);
  color: var(--primary-color, #3b82f6);
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .conversation-sidebar {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    z-index: 10;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .conversation-sidebar.show {
    transform: translateX(0);
  }

  .model-info {
    display: none;
  }

  .message-item {
    max-width: 90%;
  }
}
</style>
