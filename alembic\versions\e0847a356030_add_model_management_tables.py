"""add_model_management_tables

Revision ID: e0847a356030
Revises: befef702c9e3
Create Date: 2025-04-29 14:10:06.608645

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'e0847a356030'
down_revision: Union[str, None] = 'befef702c9e3'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('model_configurations',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False, comment='模型唯一名称'),
    sa.Column('display_name', sa.String(length=100), nullable=False, comment='模型显示名称'),
    sa.Column('description', sa.String(length=500), nullable=True, comment='模型描述'),
    sa.Column('model_type', sa.String(length=50), nullable=False, comment='模型类型: openai, deepseek, anthropic等'),
    sa.Column('model_name', sa.String(length=100), nullable=False, comment='具体模型名称: gpt-4, claude-3等'),
    sa.Column('api_key', sa.String(length=500), nullable=True, comment='API密钥(加密存储)'),
    sa.Column('base_url', sa.String(length=255), nullable=True, comment='API基础URL'),
    sa.Column('parameters', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='附加参数JSON'),
    sa.Column('input_price', sa.Float(), nullable=True, comment='输入token价格(单位:百万tokens)'),
    sa.Column('output_price', sa.Float(), nullable=True, comment='输出token价格(单位:百万tokens)'),
    sa.Column('currency', sa.String(length=10), nullable=True, comment='货币单位'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='模型是否激活'),
    sa.Column('is_custom', sa.Boolean(), nullable=True, comment='是否自定义模型'),
    sa.Column('is_visible', sa.Boolean(), nullable=True, comment='是否在用户界面可见'),
    sa.Column('capabilities', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='模型能力列表'),
    sa.Column('max_tokens', sa.Integer(), nullable=True, comment='最大token限制'),
    sa.Column('rate_limit', sa.Integer(), nullable=True, comment='每分钟最大请求次数'),
    sa.Column('user_rate_limit', sa.Integer(), nullable=True, comment='每用户每分钟最大请求次数'),
    sa.Column('concurrency_limit', sa.Integer(), nullable=True, comment='并发请求限制'),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('created_by', sa.UUID(), nullable=True),
    sa.Column('updated_by', sa.UUID(), nullable=True),
    sa.Column('total_requests', sa.Integer(), nullable=True, comment='总请求数'),
    sa.Column('total_tokens', sa.Integer(), nullable=True, comment='总tokens数'),
    sa.Column('avg_response_time', sa.Float(), nullable=True, comment='平均响应时间(ms)'),
    sa.Column('last_used_at', sa.DateTime(), nullable=True, comment='最后使用时间'),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_model_type_active', 'model_configurations', ['model_type', 'is_active'], unique=False)
    op.create_index(op.f('ix_model_configurations_model_type'), 'model_configurations', ['model_type'], unique=False)
    op.create_index(op.f('ix_model_configurations_name'), 'model_configurations', ['name'], unique=True)
    op.create_table('user_usage_stats',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('usage_date', sa.Date(), nullable=False, comment='统计日期'),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('total_requests', sa.Integer(), nullable=True, comment='总请求数'),
    sa.Column('total_input_tokens', sa.Integer(), nullable=True, comment='总输入token数'),
    sa.Column('total_output_tokens', sa.Integer(), nullable=True, comment='总输出token数'),
    sa.Column('model_distribution', sa.UUID(), nullable=True, comment='最常用模型ID'),
    sa.Column('total_cost', sa.Float(), nullable=True, comment='总成本'),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', 'usage_date'),
    postgresql_partition_by='RANGE (usage_date)'
    )
    op.create_index('idx_user_usage_date', 'user_usage_stats', ['user_id', 'usage_date'], unique=False)
    op.create_table('model_audit_logs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('model_id', sa.UUID(), nullable=False),
    sa.Column('action', sa.String(length=50), nullable=False, comment='执行的操作: create, update, delete, activate, deactivate'),
    sa.Column('entity_type', sa.String(length=50), nullable=True, comment='实体类型'),
    sa.Column('changes_summary', sa.Text(), nullable=True, comment='变更摘要'),
    sa.Column('changes_detail', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='变更详情JSON'),
    sa.Column('action_date', sa.DateTime(), nullable=False),
    sa.Column('performed_by', sa.UUID(), nullable=True),
    sa.Column('ip_address', sa.String(length=50), nullable=True, comment='操作来源IP'),
    sa.ForeignKeyConstraint(['model_id'], ['model_configurations.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['performed_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_audit_action_date', 'model_audit_logs', ['action', 'action_date'], unique=False)
    op.create_index('idx_audit_model_date', 'model_audit_logs', ['model_id', 'action_date'], unique=False)
    op.create_table('model_performance_tests',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('model_id', sa.UUID(), nullable=False),
    sa.Column('test_name', sa.String(length=100), nullable=False, comment='测试名称'),
    sa.Column('test_type', sa.String(length=50), nullable=False, comment='测试类型: standard, long_context, etc.'),
    sa.Column('rounds', sa.Integer(), nullable=False, comment='测试轮数'),
    sa.Column('avg_response_time', sa.Float(), nullable=True, comment='平均响应时间(ms)'),
    sa.Column('avg_first_token_time', sa.Float(), nullable=True, comment='平均首个token响应时间(ms)'),
    sa.Column('avg_throughput', sa.Float(), nullable=True, comment='平均吞吐量(tokens/sec)'),
    sa.Column('success_rate', sa.Float(), nullable=True, comment='成功率(%)'),
    sa.Column('error_rate', sa.Float(), nullable=True, comment='错误率(%)'),
    sa.Column('test_params', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='测试参数'),
    sa.Column('detailed_results', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='详细测试结果'),
    sa.Column('test_date', sa.DateTime(), nullable=False),
    sa.Column('tested_by', sa.UUID(), nullable=True),
    sa.ForeignKeyConstraint(['model_id'], ['model_configurations.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['tested_by'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_perf_test_model_date', 'model_performance_tests', ['model_id', 'test_date'], unique=False)
    op.create_index('idx_perf_test_type', 'model_performance_tests', ['test_type'], unique=False)
    op.create_table('model_price_history',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('model_id', sa.UUID(), nullable=False),
    sa.Column('input_price', sa.Float(), nullable=False, comment='输入token价格'),
    sa.Column('output_price', sa.Float(), nullable=False, comment='输出token价格'),
    sa.Column('currency', sa.String(length=10), nullable=True, comment='货币单位'),
    sa.Column('effective_date', sa.DateTime(), nullable=False, comment='生效日期'),
    sa.Column('changed_by', sa.UUID(), nullable=True),
    sa.ForeignKeyConstraint(['changed_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['model_id'], ['model_configurations.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_model_id_date', 'model_price_history', ['model_id', 'effective_date'], unique=False)
    op.create_index(op.f('ix_model_price_history_model_id'), 'model_price_history', ['model_id'], unique=False)
    op.create_table('model_usage_daily',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('usage_date', sa.Date(), nullable=False, comment='统计日期'),
    sa.Column('model_id', sa.UUID(), nullable=False),
    sa.Column('request_count', sa.Integer(), nullable=True, comment='请求总数'),
    sa.Column('success_count', sa.Integer(), nullable=True, comment='成功请求数'),
    sa.Column('error_count', sa.Integer(), nullable=True, comment='错误请求数'),
    sa.Column('input_tokens', sa.Integer(), nullable=True, comment='输入token总数'),
    sa.Column('output_tokens', sa.Integer(), nullable=True, comment='输出token总数'),
    sa.Column('avg_response_time', sa.Float(), nullable=True, comment='平均响应时间(ms)'),
    sa.Column('avg_first_token_time', sa.Float(), nullable=True, comment='平均首个token响应时间(ms)'),
    sa.Column('max_response_time', sa.Float(), nullable=True, comment='最大响应时间(ms)'),
    sa.Column('p95_response_time', sa.Float(), nullable=True, comment='P95响应时间(ms)'),
    sa.Column('unique_users', sa.Integer(), nullable=True, comment='唯一用户数'),
    sa.Column('user_distribution', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='用户分布统计'),
    sa.Column('estimated_cost', sa.Float(), nullable=True, comment='估计成本'),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['model_id'], ['model_configurations.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', 'usage_date'),
    postgresql_partition_by='RANGE (usage_date)'
    )
    op.create_index('idx_usage_model_date', 'model_usage_daily', ['model_id', 'usage_date'], unique=False)
    op.create_table('model_usage_hourly',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('hour_timestamp', sa.DateTime(), nullable=False, comment='小时时间戳 (YYYY-MM-DD HH:00:00)'),
    sa.Column('model_id', sa.UUID(), nullable=False),
    sa.Column('request_count', sa.Integer(), nullable=True, comment='请求总数'),
    sa.Column('success_count', sa.Integer(), nullable=True, comment='成功请求数'),
    sa.Column('error_count', sa.Integer(), nullable=True, comment='错误请求数'),
    sa.Column('input_tokens', sa.Integer(), nullable=True, comment='输入token总数'),
    sa.Column('output_tokens', sa.Integer(), nullable=True, comment='输出token总数'),
    sa.Column('avg_response_time', sa.Float(), nullable=True, comment='平均响应时间(ms)'),
    sa.Column('avg_first_token_time', sa.Float(), nullable=True, comment='平均首个token响应时间(ms)'),
    sa.Column('min_response_time', sa.Float(), nullable=True, comment='最小响应时间(ms)'),
    sa.Column('max_response_time', sa.Float(), nullable=True, comment='最大响应时间(ms)'),
    sa.Column('error_types', sa.String(length=500), nullable=True, comment='主要错误类型(逗号分隔)'),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.ForeignKeyConstraint(['model_id'], ['model_configurations.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', 'hour_timestamp'),
    postgresql_partition_by='RANGE (hour_timestamp)'
    )
    op.create_index('idx_hourly_model_hour', 'model_usage_hourly', ['model_id', 'hour_timestamp'], unique=False)
    op.create_table('model_usage_logs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('timestamp', sa.DateTime(), nullable=False, comment='请求时间'),
    sa.Column('model_id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=True),
    sa.Column('conversation_id', sa.UUID(), nullable=True),
    sa.Column('request_id', sa.String(length=64), nullable=True, comment='请求ID'),
    sa.Column('input_tokens', sa.Integer(), nullable=True, comment='输入token数'),
    sa.Column('output_tokens', sa.Integer(), nullable=True, comment='输出token数'),
    sa.Column('response_time', sa.Float(), nullable=True, comment='响应时间(ms)'),
    sa.Column('first_token_time', sa.Float(), nullable=True, comment='首个token响应时间(ms)'),
    sa.Column('status', sa.String(length=20), nullable=False, comment='请求状态: success, failed'),
    sa.Column('error_type', sa.String(length=50), nullable=True, comment='错误类型'),
    sa.Column('error_message', sa.String(length=500), nullable=True, comment='错误消息'),
    sa.Column('is_sampled', sa.Boolean(), nullable=True, comment='是否为采样记录'),
    sa.Column('is_error', sa.Boolean(), nullable=True, comment='是否为错误记录'),
    sa.Column('request_context', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='请求上下文(仅保存部分)'),
    sa.ForeignKeyConstraint(['conversation_id'], ['conversations.id'], ),
    sa.ForeignKeyConstraint(['model_id'], ['model_configurations.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ),
    sa.PrimaryKeyConstraint('id', 'timestamp'),
    postgresql_partition_by='RANGE (timestamp)'
    )
    op.create_index('idx_usage_log_model_status', 'model_usage_logs', ['model_id', 'status'], unique=False)
    op.create_index('idx_usage_log_timestamp', 'model_usage_logs', ['timestamp'], unique=False)
    op.create_index('idx_usage_log_user', 'model_usage_logs', ['user_id'], unique=False)
    op.create_table('user_model_access',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('user_id', sa.UUID(), nullable=False),
    sa.Column('model_id', sa.UUID(), nullable=False),
    sa.Column('access_level', sa.String(length=20), nullable=True, comment='访问级别: read, write, admin'),
    sa.Column('has_access', sa.Boolean(), nullable=True, comment='是否有访问权限'),
    sa.Column('daily_quota', sa.Integer(), nullable=True, comment='每日请求限制'),
    sa.Column('token_quota', sa.Integer(), nullable=True, comment='每日token限制'),
    sa.Column('custom_settings', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='用户自定义模型设置'),
    sa.Column('created_at', sa.DateTime(), nullable=False),
    sa.Column('updated_at', sa.DateTime(), nullable=False),
    sa.Column('granted_by', sa.UUID(), nullable=True),
    sa.ForeignKeyConstraint(['granted_by'], ['users.id'], ),
    sa.ForeignKeyConstraint(['model_id'], ['model_configurations.id'], ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_model_access_model', 'user_model_access', ['model_id'], unique=False)
    op.create_index('idx_model_access_user', 'user_model_access', ['user_id'], unique=False)
    op.create_index('idx_model_access_user_model', 'user_model_access', ['user_id', 'model_id'], unique=True)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('idx_model_access_user_model', table_name='user_model_access')
    op.drop_index('idx_model_access_user', table_name='user_model_access')
    op.drop_index('idx_model_access_model', table_name='user_model_access')
    op.drop_table('user_model_access')
    op.drop_index('idx_usage_log_user', table_name='model_usage_logs')
    op.drop_index('idx_usage_log_timestamp', table_name='model_usage_logs')
    op.drop_index('idx_usage_log_model_status', table_name='model_usage_logs')
    op.drop_table('model_usage_logs')
    op.drop_index('idx_hourly_model_hour', table_name='model_usage_hourly')
    op.drop_table('model_usage_hourly')
    op.drop_index('idx_usage_model_date', table_name='model_usage_daily')
    op.drop_table('model_usage_daily')
    op.drop_index(op.f('ix_model_price_history_model_id'), table_name='model_price_history')
    op.drop_index('idx_model_id_date', table_name='model_price_history')
    op.drop_table('model_price_history')
    op.drop_index('idx_perf_test_type', table_name='model_performance_tests')
    op.drop_index('idx_perf_test_model_date', table_name='model_performance_tests')
    op.drop_table('model_performance_tests')
    op.drop_index('idx_audit_model_date', table_name='model_audit_logs')
    op.drop_index('idx_audit_action_date', table_name='model_audit_logs')
    op.drop_table('model_audit_logs')
    op.drop_index('idx_user_usage_date', table_name='user_usage_stats')
    op.drop_table('user_usage_stats')
    op.drop_index(op.f('ix_model_configurations_name'), table_name='model_configurations')
    op.drop_index(op.f('ix_model_configurations_model_type'), table_name='model_configurations')
    op.drop_index('idx_model_type_active', table_name='model_configurations')
    op.drop_table('model_configurations')
    # ### end Alembic commands ###
