<template>
  <div class="test-composables-view">
    <div class="header">
      <h1>Phase 5: 组合式函数库测试</h1>
      <p class="description">测试所有组合式函数的功能和集成</p>
    </div>

    <div class="main-content">
      <!-- 左侧测试区域 -->
      <div class="test-sections">
        <!-- 认证测试 -->
        <div class="test-section">
        <h2>🔐 认证管理 (useAuth)</h2>
        <div class="test-content">
          <div class="status-info">
            <p><strong>登录状态:</strong> {{ auth.isAuthenticated ? '已登录' : '未登录' }}</p>
            <p><strong>用户角色:</strong> {{ auth.userRole }}</p>
            <p><strong>用户名:</strong> {{ auth.userName || '(空)' }}</p>
            <p><strong>用户邮箱:</strong> {{ auth.userEmail || '(空)' }}</p>
            <p><strong>是否管理员:</strong> {{ auth.isAdmin ? '是' : '否' }}</p>
            <p><strong>用户对象:</strong> {{ auth.user ? '存在' : '不存在' }}</p>
            <p><strong>Token状态:</strong> {{ auth.isAuthenticated ? '有效' : '无效' }}</p>
            <p><strong>最后错误:</strong> {{ auth.lastError || '无' }}</p>
          </div>
          <div class="actions">
            <button @click="debugUserData" class="btn">调试用户数据</button>
            <button @click="testPermission" class="btn">测试权限检查</button>
            <button @click="testTokenRefresh" class="btn">测试Token刷新</button>
            <button @click="testRouteGuard" class="btn">测试路由守卫</button>
          </div>
        </div>
      </div>

      <!-- 聊天测试 -->
      <div class="test-section">
        <h2>💬 聊天功能 (useChat)</h2>
        <div class="test-content">
          <div class="status-info">
            <p><strong>当前对话:</strong> {{ currentConversationTitle }}</p>
            <p><strong>对话数量:</strong> {{ conversationsLength }}</p>
            <p><strong>选中模型:</strong> {{ selectedModelName }}</p>
            <p v-if="models.selectedModel.value"><strong>模型描述:</strong> {{ models.selectedModel.value.description || '无描述' }}</p>
            <p v-if="models.selectedModel.value && models.selectedModel.value.capabilities"><strong>模型能力:</strong> {{ models.selectedModel.value.capabilities.join(', ') }}</p>
            <p><strong>连接状态:</strong>
              <span :class="chat.connectionStatus">{{ chat.connectionStatus }}</span>
            </p>
            <p><strong>模型支持推理:</strong> {{ supportsReasoning ? '是' : '否' }}</p>
            <p><strong>发送状态:</strong> {{ chat.isSending ? '发送中' : '空闲' }}</p>
          </div>
          <div class="actions">
            <input
              v-model="chatMessageInput"
              placeholder="输入测试消息..."
              @keypress="chat.handleKeyPress"
              class="input"
            />
            <button @click="testCreateConversation" class="btn">创建对话</button>
            <button @click="testSendMessage" class="btn" :disabled="!messageInputValue || !messageInputValue.trim()">
              发送消息
            </button>
            <button @click="testSendStreamMessage" class="btn" :disabled="!messageInputValue || !messageInputValue.trim()">
              发送流式消息
            </button>
            <button @click="testRefreshConversations" class="btn">刷新对话</button>
            <button @click="refreshCurrentConversation" class="btn">刷新当前对话</button>
            <button @click="testConversationAPI" class="btn">测试对话API</button>
          </div>

          <!-- 对话列表显示区域 -->
          <div v-if="showConversationList" class="conversation-list-display">
            <div class="conversation-list-header">
              <h4>📋 对话列表 ({{ conversationsLength }})</h4>
              <div class="conversation-list-controls">
                <button @click="refreshConversationList" class="btn btn-small">刷新列表</button>
                <button @click="toggleConversationList" class="btn btn-small">
                  {{ showConversationList ? '隐藏' : '显示' }}列表
                </button>
              </div>
            </div>

            <div class="conversation-list-content">
              <div v-if="conversationsLength === 0" class="empty-state">
                <p>暂无对话记录</p>
                <p class="text-secondary">创建第一个对话开始聊天吧！</p>
              </div>

              <div v-else class="conversation-items">
                <div
                  v-for="conversation in sortedConversationsValue"
                  :key="conversation.id"
                  class="conversation-item"
                  :class="{
                    'active': conversation.id === chat.currentConversation?.id,
                    'archived': conversation.is_archived
                  }"
                  @click="selectConversation(conversation.id)"
                >
                  <div class="conversation-item-header">
                    <h5 class="conversation-title">{{ conversation.title || '未命名对话' }}</h5>
                    <span class="conversation-time">{{ formatMessageTime(conversation.updated_at) }}</span>
                  </div>

                  <div class="conversation-item-content">
                    <p class="last-message">{{ conversation.last_message || '暂无消息' }}</p>
                    <div class="conversation-meta">
                      <span class="message-count">{{ conversation.message_count || 0 }} 条消息</span>
                      <span v-if="conversation.model_name" class="model-name">{{ conversation.model_name }}</span>
                      <span v-if="conversation.tags?.length" class="tags">
                        <span v-for="tag in conversation.tags" :key="tag" class="tag">{{ tag }}</span>
                      </span>
                    </div>
                  </div>

                  <div class="conversation-actions">
                    <button @click.stop="switchToConversation(conversation.id)" class="btn btn-mini">切换</button>
                    <button @click.stop="deleteConversationFromList(conversation.id)" class="btn btn-mini danger">删除</button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 对话历史显示区域 -->
          <div v-if="showConversationHistory" class="conversation-display">
            <div class="conversation-header">
              <h4>💬 当前对话历史</h4>
              <div class="conversation-info">
                <span>对话ID: {{ chat.currentConversation?.id || '无' }}</span>
                <span>消息数: {{ conversationMessages.length }}</span>
                <button @click="toggleConversationHistory" class="btn btn-small">
                  {{ showConversationHistory ? '隐藏' : '显示' }}对话
                </button>
              </div>
            </div>

            <div class="messages-container">
              <!-- 历史消息 -->
              <div
                v-for="(message, index) in conversationMessages"
                :key="message.id || index"
                class="message-item"
                :class="message.role"
              >
                <div class="message-header">
                  <span class="message-role">
                    {{ message.role === 'user' ? '👤 用户' : '🤖 AI' }}
                  </span>
                  <span class="message-time">
                    {{ formatMessageTime(message.created_at) }}
                  </span>
                </div>

                <!-- Thinking内容（仅AI消息且有thinking时显示） -->
                <div v-if="message.role === 'assistant' && message.thinking" class="thinking-content">
                  <div class="thinking-header">
                    <span class="thinking-icon">🤔</span>
                    <strong>思考过程:</strong>
                  </div>
                  <div class="thinking-text">{{ message.thinking }}</div>
                </div>

                <!-- 消息内容 -->
                <div class="message-content">{{ message.content }}</div>

                <!-- 消息状态 -->
                <div v-if="message.status" class="message-status">
                  状态: {{ message.status }}
                </div>
              </div>

              <!-- 当前流式消息（如果正在发送） -->
              <div v-if="streamingMessage" class="message-item streaming">
                <div class="message-header">
                  <span class="message-role">👤 用户</span>
                  <span class="message-time">刚刚</span>
                </div>
                <div class="message-content">{{ streamingMessage.userMessage }}</div>
              </div>

              <div v-if="streamingMessage" class="message-item assistant streaming">
                <div class="message-header">
                  <span class="message-role">🤖 AI</span>
                  <span class="message-time">
                    {{ streamingMessage.isComplete ? '刚刚' : '正在回复...' }}
                  </span>
                </div>

                <!-- 当前thinking内容 -->
                <div v-if="streamingMessage.thinking || streamingMessage.isThinking" class="thinking-content">
                  <div class="thinking-header">
                    <span class="thinking-icon">🤔</span>
                    <strong>思考过程:</strong>
                    <span v-if="streamingMessage.isThinking" class="thinking-indicator">思考中...</span>
                  </div>
                  <div class="thinking-text">{{ streamingMessage.thinking }}</div>
                </div>

                <!-- 当前AI回复内容 -->
                <div class="message-content">{{ streamingMessage.content }}</div>

                <!-- 状态指示器 -->
                <div class="message-status">
                  <span v-if="streamingMessage.isComplete" class="complete-indicator">✅ 回复完成</span>
                  <span v-else-if="streamingMessage.content" class="streaming-indicator">⏳ 正在生成...</span>
                  <span v-else-if="streamingMessage.isThinking" class="thinking-indicator">🤔 正在思考...</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 模型测试 -->
      <div class="test-section">
        <h2>🤖 模型管理 (useModels)</h2>
        <div class="test-content">
          <div class="status-info">
            <p><strong>可用模型:</strong> {{ models.modelCount }}</p>
            <p><strong>活跃模型:</strong> {{ activeModelsLength }}</p>
            <p><strong>选中模型:</strong> {{ selectedModelName }}</p>
            <p v-if="models.selectedModel.value"><strong>模型描述:</strong> {{ models.selectedModel.value.description || '无描述' }}</p>
            <p v-if="models.selectedModel.value && models.selectedModel.value.capabilities"><strong>模型能力:</strong> {{ models.selectedModel.value.capabilities.join(', ') }}</p>
            <p><strong>测试状态:</strong> {{ models.isTesting ? '测试中' : '空闲' }}</p>
          </div>
          <div class="actions">
            <div class="model-selection">
              <select @change="selectModel" class="select" v-model="selectedModelForTest">
                <option value="">选择模型</option>
                <option
                  v-for="model in activeModelsValue"
                  :key="model.id"
                  :value="model.id"
                >
                  {{ model.display_name || model.name || model.id }}
                </option>
              </select>
              <button @click="testFetchModels" class="btn">获取模型列表</button>
            </div>
            <div class="model-actions">
              <button @click="testModelPerformance" class="btn">测试性能</button>
              <button @click="testModelRecommendation" class="btn">获取推荐</button>
              <button @click="testRefreshModels" class="btn">刷新模型</button>
            </div>
          </div>
        </div>
      </div>

      <!-- 计费测试 -->
      <div class="test-section">
        <h2>💰 计费管理 (useBilling)</h2>
        <div class="test-content">
          <div class="status-info">
            <p><strong>总使用量:</strong> {{ billing.formatNumber(totalTokensUsedValue) }} tokens</p>
            <p><strong>总费用:</strong> {{ billing.formatCurrency(totalCostValue) }}</p>
            <p><strong>使用百分比:</strong> {{ usagePercentageValue.toFixed(1) }}%</p>
            <p><strong>状态:</strong>
              <span :class="usageStatusValue.type">{{ usageStatusValue.text }}</span>
            </p>
          </div>
          <div class="actions">
            <button @click="testFetchUsage" class="btn">获取使用统计</button>
            <button @click="testGenerateReport" class="btn">生成报告</button>
            <button @click="testMonitoring" class="btn">运行监控</button>
            <button @click="toggleAutoRefresh" class="btn">
              {{ billing.autoRefreshEnabled ? '停止' : '启动' }}自动刷新
            </button>
          </div>
        </div>
      </div>

      <!-- 主题测试 -->
      <div class="test-section">
        <h2>🎨 主题管理 (useTheme)</h2>
        <div class="test-content">
          <div class="status-info">
            <p><strong>当前主题:</strong> {{ theme.getThemeDisplayName(currentThemeValue) }}</p>
            <p><strong>布局模式:</strong> {{ theme.layoutMode }}</p>
            <p><strong>字体大小:</strong> {{ theme.fontSize }}px</p>
            <p><strong>设备类型:</strong> 
              {{ theme.isMobile ? '手机' : theme.isTablet ? '平板' : '桌面' }}
            </p>
          </div>
          <div class="actions">
            <button @click="theme.toggleTheme" class="btn">切换主题</button>
            <button @click="theme.toggleSidebar" class="btn">切换侧边栏</button>
            <button @click="theme.increaseFontSize" class="btn">放大字体</button>
            <button @click="theme.decreaseFontSize" class="btn">缩小字体</button>
            <button @click="theme.toggleAnimations" class="btn">切换动画</button>
          </div>
        </div>
      </div>

      <!-- 通知测试 -->
      <div class="test-section">
        <h2>🔔 通知系统 (useNotification)</h2>
        <div class="test-content">
          <div class="status-info">
            <p><strong>通知数量:</strong> {{ notification.notificationCount }}</p>
            <p><strong>未读数量:</strong> {{ notification.unreadCount }}</p>
            <p><strong>成功通知:</strong> {{ notificationsByTypeValue.success.length }}</p>
            <p><strong>错误通知:</strong> {{ notificationsByTypeValue.error.length }}</p>
          </div>
          <div class="actions">
            <button @click="testSuccessNotification" class="btn success">成功通知</button>
            <button @click="testWarningNotification" class="btn warning">警告通知</button>
            <button @click="testErrorNotification" class="btn error">错误通知</button>
            <button @click="testConfirmNotification" class="btn">确认通知</button>
            <button @click="notification.clearAll" class="btn">清除所有</button>
          </div>
        </div>
      </div>

      <!-- 权限测试 -->
      <div class="test-section">
        <h2>🛡️ 权限控制 (usePermission)</h2>
        <div class="test-content">
          <div class="status-info">
            <p><strong>用户权限数:</strong> {{ permission.getUserPermissions().length }}</p>
            <p><strong>管理员权限:</strong> {{ permission.hasRole('admin') ? '有' : '无' }}</p>
            <p><strong>模型管理权限:</strong> {{ permission.hasPermission(permission.PERMISSIONS.MODEL_MANAGE) ? '有' : '无' }}</p>
          </div>
          <div class="actions">
            <button @click="testPermissionCheck" class="btn">检查权限</button>
            <button @click="testRoleCheck" class="btn">检查角色</button>
            <button @click="testAdminAccess" class="btn">测试管理员访问</button>
            <button @click="testPermissionGuard" class="btn">测试权限守卫</button>
          </div>
        </div>
      </div>

      <!-- 表单验证测试 -->
      <div class="test-section">
        <h2>✅ 表单验证 (useValidation)</h2>
        <div class="test-content">
          <div class="status-info">
            <p><strong>表单有效:</strong> {{ validation.isFormValid ? '是' : '否' }}</p>
            <p><strong>错误数量:</strong> {{ validation.errorCount }}</p>
            <p><strong>已触摸字段:</strong> {{ touchedFieldsLength }}</p>
          </div>
          <div class="form-test">
            <div class="field">
              <label>用户名:</label>
              <input
                v-model="usernameValue"
                @blur="validation.touchField('username')"
                @input="validation.validateField('username')"
                class="input"
                :class="{ error: validation.form.username?.error }"
              />
              <span v-if="validation.form.username?.error" class="error-text">
                {{ validation.form.username.error }}
              </span>
            </div>
            <div class="field">
              <label>邮箱:</label>
              <input
                v-model="emailValue"
                @blur="validation.touchField('email')"
                @input="validation.validateField('email')"
                class="input"
                :class="{ error: validation.form.email?.error }"
              />
              <span v-if="validation.form.email?.error" class="error-text">
                {{ validation.form.email.error }}
              </span>
            </div>
          </div>
          <div class="actions">
            <button @click="initValidationForm" class="btn">初始化表单</button>
            <button @click="testFormValidation" class="btn">验证表单</button>
            <button @click="validation.resetForm" class="btn">重置表单</button>
          </div>
        </div>
      </div>

      <!-- WebSocket测试 -->
      <div class="test-section">
        <h2>🔌 WebSocket通信 (useWebSocket)</h2>
        <div class="test-content">
          <div class="status-info">
            <p><strong>连接状态:</strong> 
              <span :class="websocket.connectionStatus">{{ websocket.connectionStatus }}</span>
            </p>
            <p><strong>重连次数:</strong> {{ websocket.reconnectAttempts }}</p>
            <p><strong>可以重连:</strong> {{ websocket.canReconnect ? '是' : '否' }}</p>
            <p><strong>最后错误:</strong> {{ websocket.lastError || '无' }}</p>
          </div>
          <div class="actions">
            <button @click="websocket.connect" class="btn" :disabled="isConnectedValue">
              连接
            </button>
            <button @click="websocket.disconnect" class="btn" :disabled="!isConnectedValue">
              断开
            </button>
            <button @click="testWebSocketMessage" class="btn" :disabled="!isConnectedValue">
              发送测试消息
            </button>
          </div>
        </div>
      </div>
      </div>

      <!-- 右侧测试结果 -->
      <div class="test-results">
        <div class="results-header">
          <h2>测试结果</h2>
          <button @click="clearTestResults" class="btn btn-small">清除日志</button>
        </div>
        <div class="results-content">
          <div v-for="result in testResults" :key="result.id" class="result-item" :class="result.type">
            <span class="timestamp">{{ formatTime(result.timestamp) }}</span>
            <span class="message">{{ result.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<!-- @ts-nocheck -->
<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  useAuth,
  useChat,
  useModels,
  useBilling,
  useTheme,
  useNotification,
  usePermission,
  useValidation,
  useWebSocket
} from '@/composables'
import { useAuthStore } from '@/stores/auth'

// 初始化所有composables
const auth = useAuth()
const chat = useChat()
const models = useModels()
const billing = useBilling()
const theme = useTheme()
const notification = useNotification()
const permission = usePermission()
const validation = useValidation()
const websocket = useWebSocket()

// 测试结果
const testResults = ref<Array<{
  id: string
  type: 'success' | 'error' | 'info'
  message: string
  timestamp: Date
}>>([])

// 本地计算属性，用于解决Vue模板类型检查问题
const currentConversationTitle = computed(() => chat.currentConversation.value?.title || '无')
const conversationsLength = computed(() => chat.conversations.value?.length || 0)
const selectedModelName = computed(() =>
  chat.selectedModel.value?.display_name ||
  chat.selectedModel.value?.name ||
  models.selectedModel.value?.display_name ||
  models.selectedModel.value?.name ||
  '未选择'
)
const activeModelsLength = computed(() => models.activeModels.value?.length || 0)
const activeModelsValue = computed(() => models.activeModels.value || [])
const messageInputValue = computed(() => chatMessageInput.value || '')
const totalTokensUsedValue = computed(() => billing.totalTokensUsed.value || 0)
const totalCostValue = computed(() => billing.totalCost.value || 0)
const usagePercentageValue = computed(() => billing.usagePercentage.value || 0)
const usageStatusValue = computed(() => billing.usageStatus.value || { type: 'info', text: '未知' })
const currentThemeValue = computed(() => theme.currentTheme.value || 'light')
const notificationsByTypeValue = computed(() => notification.notificationsByType.value || { success: [], error: [], warning: [], info: [] })
const touchedFieldsLength = computed(() => validation.touchedFields.value?.length || 0)
const isConnectedValue = computed(() => websocket.isConnected.value || false)

// 检查当前选中的模型是否支持推理
const supportsReasoning = computed(() => {
  const selectedModel = models.selectedModel.value
  return selectedModel?.capabilities?.includes('reasoning') || false
})

// 获取当前对话的消息列表
const conversationMessages = computed(() => {
  return chat.currentMessages.value || []
})

// 获取排序后的对话列表
const sortedConversationsValue = computed(() => {
  return chat.sortedConversations.value || []
})

// 表单字段的计算属性
const usernameValue = computed({
  get: () => validation.form.username?.value || '',
  set: (value: string) => {
    if (validation.form.username) {
      validation.form.username.value = value
    }
  }
})

const emailValue = computed({
  get: () => validation.form.email?.value || '',
  set: (value: string) => {
    if (validation.form.email) {
      validation.form.email.value = value
    }
  }
})

// 聊天输入框的计算属性
const chatMessageInput = computed({
  get: () => chat.messageInput.value || '',
  set: (value: string) => {
    chat.messageInput.value = value
  }
})

// 模型选择状态
const selectedModelForTest = ref('')

// 流式消息状态
const streamingMessage = ref<{
  userMessage: string
  content: string
  thinking: string
  isThinking: boolean
  isComplete: boolean
} | null>(null)

// 对话历史显示控制
const showConversationHistory = ref(true)

// 对话列表显示控制
const showConversationList = ref(true)

// 计数器确保唯一ID
let resultCounter = 0

// 添加测试结果
const addTestResult = (type: 'success' | 'error' | 'info', message: string) => {
  resultCounter++
  testResults.value.unshift({
    id: `result-${resultCounter}-${Date.now()}`,
    type,
    message,
    timestamp: new Date()
  })

  // 限制结果数量
  if (testResults.value.length > 50) {
    testResults.value = testResults.value.slice(0, 50)
  }
}

// 格式化时间
const formatTime = (date: Date): string => {
  return date.toLocaleTimeString()
}

// 格式化消息时间
const formatMessageTime = (dateString: string): string => {
  if (!dateString) return '未知时间'

  try {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    if (diffMins < 1) return '刚刚'
    if (diffMins < 60) return `${diffMins}分钟前`
    if (diffHours < 24) return `${diffHours}小时前`
    if (diffDays < 7) return `${diffDays}天前`

    return date.toLocaleDateString()
  } catch (error) {
    return '时间格式错误'
  }
}

// 切换对话历史显示
const toggleConversationHistory = (): void => {
  showConversationHistory.value = !showConversationHistory.value
  addTestResult('info', `对话历史显示: ${showConversationHistory.value ? '开启' : '关闭'}`)
}

// 切换对话列表显示
const toggleConversationList = (): void => {
  showConversationList.value = !showConversationList.value
  addTestResult('info', `对话列表显示: ${showConversationList.value ? '开启' : '关闭'}`)
}

// 刷新对话列表
const refreshConversationList = async (): Promise<void> => {
  try {
    addTestResult('info', '正在刷新对话列表...')
    await chat.refreshConversations()

    addTestResult('success', `对话列表已刷新，共 ${conversationsLength.value} 个对话`)

    // 显示对话列表详情
    if (conversationsLength.value > 0) {
      addTestResult('info', '=== 对话列表 ===')
      chat.conversations.value?.forEach((conv, index) => {
        const preview = conv.last_message?.substring(0, 30) + (conv.last_message && conv.last_message.length > 30 ? '...' : '') || '暂无消息'
        addTestResult('info', `${index + 1}. ${conv.title || '未命名'}: ${preview} (${conv.message_count || 0}条消息)`)
      })
      addTestResult('info', '================')
    }

  } catch (error) {
    addTestResult('error', `刷新对话列表失败: ${error}`)
  }
}

// 选择对话（仅高亮，不切换）
const selectConversation = (conversationId: string): void => {
  const conversation = chat.conversations.value?.find(c => c.id === conversationId)
  if (conversation) {
    addTestResult('info', `选择对话: ${conversation.title || '未命名'} (${conversationId})`)
    addTestResult('info', `最后消息: ${conversation.last_message || '暂无'}`)
    addTestResult('info', `消息数量: ${conversation.message_count || 0}`)
    addTestResult('info', `更新时间: ${conversation.updated_at}`)
  }
}

// 切换到指定对话
const switchToConversation = async (conversationId: string): Promise<void> => {
  try {
    addTestResult('info', `正在切换到对话: ${conversationId}`)
    await chat.switchConversation(conversationId)

    addTestResult('success', '对话切换成功')
    addTestResult('info', `当前对话: ${chat.currentConversation.value?.title || '未知'}`)
    addTestResult('info', `当前消息数: ${conversationMessages.value.length}`)

  } catch (error) {
    addTestResult('error', `切换对话失败: ${error}`)
  }
}

// 从列表中删除对话
const deleteConversationFromList = async (conversationId: string): Promise<void> => {
  try {
    const conversation = chat.conversations.value?.find(c => c.id === conversationId)
    const title = conversation?.title || '未命名对话'

    addTestResult('info', `正在删除对话: ${title}`)

    const success = await chat.deleteConversation(conversationId)

    if (success) {
      addTestResult('success', `对话 "${title}" 已删除`)
      // 刷新列表
      await refreshConversationList()
    } else {
      addTestResult('error', `删除对话 "${title}" 失败`)
    }

  } catch (error) {
    addTestResult('error', `删除对话失败: ${error}`)
  }
}

// 清除测试结果
const clearTestResults = (): void => {
  testResults.value = []
  addTestResult('info', '测试日志已清除')
}

// 获取authStore实例
const authStore = useAuthStore()

// 调试用户数据
const debugUserData = () => {
  try {
    addTestResult('info', '=== 用户数据调试 ===')

    addTestResult('info', `认证状态: ${authStore.isAuthenticated}`)
    addTestResult('info', `Token存在: ${!!authStore.token}`)
    addTestResult('info', `用户对象: ${JSON.stringify(authStore.user, null, 2)}`)
    addTestResult('info', `用户角色: ${authStore.userRole}`)
    addTestResult('info', `是否管理员: ${authStore.isAdmin}`)
    addTestResult('info', `用户名: "${authStore.userName}"`)
    addTestResult('info', `用户邮箱: "${authStore.userEmail}"`)
    addTestResult('info', `最后错误: ${authStore.lastError}`)
    addTestResult('info', `最后登录时间: ${authStore.lastLoginTime}`)

    // 检查本地存储
    const storedUser = localStorage.getItem('auth-store')
    addTestResult('info', `本地存储数据: ${storedUser}`)

    // 检查权限函数
    const hasPermissionFunc = authStore.hasPermission
    addTestResult('info', `权限检查函数: ${typeof hasPermissionFunc}`)

    if (authStore.user) {
      addTestResult('info', `用户字段详情:`)
      Object.keys(authStore.user).forEach(key => {
        addTestResult('info', `  ${key}: ${JSON.stringify(authStore.user[key])}`)
      })
    }

    // 测试useAuth composable的数据
    addTestResult('info', '=== useAuth Composable 数据 ===')
    addTestResult('info', `useAuth.isAuthenticated: ${auth.isAuthenticated.value}`)
    addTestResult('info', `useAuth.user: ${JSON.stringify(auth.user.value)}`)
    addTestResult('info', `useAuth.userRole: ${auth.userRole.value}`)
    addTestResult('info', `useAuth.isAdmin: ${auth.isAdmin.value}`)
    addTestResult('info', `useAuth.userName: "${auth.userName.value}"`)
    addTestResult('info', `useAuth.userEmail: "${auth.userEmail.value}"`)

  } catch (error) {
    addTestResult('error', `调试失败: ${error}`)
  }
}

// 认证测试
const testPermission = () => {
  try {
    // 测试多个权限
    const permissions = [
      'model:view',
      'chat:create',
      'billing:view',
      'admin:panel'
    ]

    addTestResult('info', `开始权限检查测试...`)
    addTestResult('info', `用户数据: ${JSON.stringify(auth.user.value)}`)

    permissions.forEach(permission => {
      try {
        const hasAccess = auth.hasPermission(permission)
        addTestResult(
          hasAccess ? 'success' : 'info',
          `权限检查: ${permission} - ${hasAccess ? '有权限' : '无权限'}`
        )
      } catch (error) {
        addTestResult('error', `权限检查失败 ${permission}: ${error}`)
      }
    })

    // 测试组合权限
    try {
      const hasAnyPermission = auth.hasAnyPermission(['model:view', 'chat:create'])
      const hasAllPermissions = auth.hasAllPermissions(['model:view', 'chat:create'])

      addTestResult('info', `任一权限: ${hasAnyPermission}`)
      addTestResult('info', `全部权限: ${hasAllPermissions}`)
    } catch (error) {
      addTestResult('error', `组合权限检查失败: ${error}`)
    }

  } catch (error) {
    addTestResult('error', `权限检查失败: ${error}`)
  }
}

const testTokenRefresh = async () => {
  try {
    addTestResult('info', '开始Token刷新测试...')

    if (!auth.isAuthenticated.value) {
      addTestResult('error', 'Token刷新失败: 用户未登录')
      return
    }

    const result = await auth.checkAndRefreshToken()
    addTestResult('success', `Token刷新: ${result ? '成功刷新' : '无需刷新'}`)

    // 测试用户信息刷新
    const userRefresh = await auth.refreshUser()
    addTestResult('info', `用户信息刷新: ${userRefresh ? '成功' : '失败'}`)

  } catch (error) {
    addTestResult('error', `Token刷新失败: ${error}`)
  }
}

const testRouteGuard = () => {
  try {
    addTestResult('info', '开始路由守卫测试...')

    // 测试基础认证
    const canAccess = auth.requireAuth()
    addTestResult(
      canAccess ? 'success' : 'error',
      `基础认证守卫: ${canAccess ? '通过' : '拒绝'}`
    )

    // 测试权限守卫
    const canManageModels = auth.requirePermission('model:manage')
    addTestResult(
      canManageModels ? 'success' : 'info',
      `权限守卫(model:manage): ${canManageModels ? '通过' : '拒绝'}`
    )

    // 测试管理员守卫
    const canAccessAdmin = auth.requireAdmin()
    addTestResult(
      canAccessAdmin ? 'success' : 'info',
      `管理员守卫: ${canAccessAdmin ? '通过' : '拒绝'}`
    )

  } catch (error) {
    addTestResult('error', `路由守卫测试失败: ${error}`)
  }
}

// 聊天测试
const testCreateConversation = async () => {
  try {
    // 检查是否选择了模型
    if (!chat.ensureModelSelected()) {
      addTestResult('error', '创建对话失败: 请先选择一个聊天模型')
      return
    }

    const selectedModel = models.selectedModel.value
    addTestResult('info', `使用模型: ${selectedModel?.display_name || selectedModel?.name}`)
    if (selectedModel?.description) {
      addTestResult('info', `模型描述: ${selectedModel.description}`)
    }
    if (selectedModel?.capabilities) {
      addTestResult('info', `模型能力: ${selectedModel.capabilities.join(', ')}`)
    }

    const id = await chat.createConversation('测试对话', '你好，这是一个测试对话')
    addTestResult('success', `创建对话成功: ${id}`)

    // 立即检查状态
    addTestResult('info', `创建后立即检查 - 当前对话ID: ${chat.currentConversationId.value || '无'}`)
    addTestResult('info', `创建后立即检查 - 对话列表长度: ${conversationsLength.value}`)

    // 等待一下让状态更新
    setTimeout(() => {
      addTestResult('info', `延迟检查 - 当前对话ID: ${chat.currentConversationId.value || '无'}`)
      addTestResult('info', `延迟检查 - 当前对话对象: ${chat.currentConversation.value ? '存在' : '不存在'}`)
      addTestResult('info', `延迟检查 - 当前对话标题: ${chat.currentConversation.value?.title || '无'}`)
      addTestResult('info', `延迟检查 - 当前消息数: ${conversationMessages.value.length}`)

      // 检查对话列表中是否包含新创建的对话
      const foundConversation = chat.conversations.value?.find(c => c.id === id)
      addTestResult('info', `对话列表中是否找到新对话: ${foundConversation ? '是' : '否'}`)
      if (foundConversation) {
        addTestResult('info', `找到的对话标题: ${foundConversation.title}`)
      }
    }, 200)

  } catch (error) {
    addTestResult('error', `创建对话失败: ${error}`)
  }
}

const testSendMessage = async () => {
  try {
    // 检查是否选择了模型
    if (!chat.ensureModelSelected()) {
      addTestResult('error', '发送消息失败: 请先选择一个聊天模型')
      return
    }

    const selectedModel = models.selectedModel.value
    addTestResult('info', `发送普通消息，使用模型: ${selectedModel?.display_name || selectedModel?.name}`)
    if (selectedModel?.capabilities) {
      addTestResult('info', `模型能力: ${selectedModel.capabilities.join(', ')}`)
    }

    const message = await chat.quickSend()
    addTestResult('success', `发送消息成功: ${message?.id}`)
  } catch (error) {
    addTestResult('error', `发送消息失败: ${error}`)
  }
}

// 新增：测试流式消息发送
const testSendStreamMessage = async () => {
  try {
    // 检查是否选择了模型
    if (!chat.ensureModelSelected()) {
      addTestResult('error', '发送流式消息失败: 请先选择一个聊天模型')
      return
    }

    const selectedModel = models.selectedModel.value
    addTestResult('info', `发送流式消息，使用模型: ${selectedModel?.display_name || selectedModel?.name}`)
    if (selectedModel?.capabilities) {
      addTestResult('info', `模型能力: ${selectedModel.capabilities.join(', ')}`)

      // 检查是否支持推理
      const supportsReasoning = selectedModel.capabilities.includes('reasoning')
      addTestResult('info', `支持推理: ${supportsReasoning ? '是' : '否'}`)
    }

    const messageContent = chat.messageInput.value
    if (!messageContent.trim()) {
      addTestResult('error', '消息内容不能为空')
      return
    }

    // 初始化流式消息状态
    streamingMessage.value = {
      userMessage: messageContent,
      content: '',
      thinking: '',
      isThinking: false,
      isComplete: false
    }

    addTestResult('success', `开始发送流式消息: "${messageContent}"`)

    // 调用流式消息API
    const message = await chat.sendStreamMessage(
      messageContent,
      chat.currentConversation.value?.id,
      (chunk) => {
        // 处理流式数据块
        if (!streamingMessage.value) return

        switch (chunk.type) {
          case 'thinking_mode':
            streamingMessage.value.isThinking = chunk.isThinking
            if (chunk.isThinking) {
              addTestResult('info', '🤔 AI开始思考...')
            }
            break

          case 'thinking_chunk':
            streamingMessage.value.thinking += chunk.content
            addTestResult('info', `思考片段: ${chunk.content}`)
            break

          case 'thinking_complete':
            streamingMessage.value.thinking = chunk.content
            streamingMessage.value.isThinking = false
            addTestResult('info', '🧠 思考完成，开始生成回复')
            break

          case 'content':
            streamingMessage.value.content += chunk.content
            addTestResult('info', `内容片段: ${chunk.content}`)
            break

          case 'complete':
            streamingMessage.value.isComplete = true
            streamingMessage.value.content = chunk.message.content
            if (chunk.message.thinking) {
              streamingMessage.value.thinking = chunk.message.thinking
            }
            addTestResult('success', '✅ 流式消息完成')

            // 显示完整的对话内容
            addTestResult('info', '=== 完整对话 ===')
            addTestResult('info', `用户: ${messageContent}`)
            if (chunk.message.thinking) {
              addTestResult('info', `AI思考: ${chunk.message.thinking}`)
            }
            addTestResult('info', `AI回复: ${chunk.message.content}`)
            addTestResult('info', '==================')

            // 刷新对话历史
            setTimeout(() => {
              chat.refreshConversations()
              addTestResult('info', '对话历史已刷新')
            }, 500)
            break
        }
      }
    )

    if (message) {
      addTestResult('success', `流式消息发送成功，消息ID: ${message.id}`)
    } else {
      addTestResult('error', '流式消息发送失败')
      // 清除流式消息状态
      streamingMessage.value = null
    }

  } catch (error) {
    addTestResult('error', `发送流式消息失败: ${error}`)
    // 清除流式消息状态
    streamingMessage.value = null
  }
}

const testRefreshConversations = async () => {
  try {
    await chat.refreshConversations()
    addTestResult('success', '刷新对话列表成功')
  } catch (error) {
    addTestResult('error', `刷新对话列表失败: ${error}`)
  }
}

// 刷新当前对话的消息
const refreshCurrentConversation = async () => {
  try {
    const currentConv = chat.currentConversation.value
    if (!currentConv) {
      addTestResult('error', '没有当前对话可刷新')
      return
    }

    addTestResult('info', `刷新对话: ${currentConv.title} (${currentConv.id})`)
    addTestResult('info', `使用API: GET /api/v1/chat/conversations/${currentConv.id}`)

    // 重新设置当前对话以刷新消息
    await chat.switchConversation(currentConv.id)

    addTestResult('success', `对话消息已刷新，当前消息数: ${conversationMessages.value.length}`)

    // 显示消息详情
    if (conversationMessages.value.length > 0) {
      addTestResult('info', '=== 对话消息列表 ===')
      conversationMessages.value.forEach((msg, index) => {
        const preview = msg.content.substring(0, 50) + (msg.content.length > 50 ? '...' : '')
        const thinking = msg.thinking ? ' [有思考内容]' : ''
        addTestResult('info', `${index + 1}. ${msg.role}: ${preview}${thinking}`)
      })
      addTestResult('info', '=====================')
    } else {
      addTestResult('warning', '对话中没有消息')
    }

  } catch (error) {
    addTestResult('error', `刷新当前对话失败: ${error}`)
    addTestResult('error', '请检查后端API是否正常运行')
  }
}

// 测试对话API
const testConversationAPI = async () => {
  try {
    const currentConv = chat.currentConversation.value
    if (!currentConv) {
      addTestResult('error', '没有当前对话可测试')
      return
    }

    addTestResult('info', `测试对话API: ${currentConv.id}`)

    // 直接调用API服务测试
    const { chatService } = await import('@/services')

    addTestResult('info', '正在调用 chatService.getConversation()...')
    const response = await chatService.getConversation(currentConv.id)

    addTestResult('success', 'API调用成功')
    addTestResult('info', `对话标题: ${response.title}`)
    addTestResult('info', `消息数量: ${response.messages?.length || 0}`)
    addTestResult('info', `对话ID: ${response.id}`)
    addTestResult('info', `创建时间: ${response.created_at}`)
    addTestResult('info', `更新时间: ${response.updated_at}`)

    if (response.messages && response.messages.length > 0) {
      addTestResult('info', '=== API返回的消息 ===')
      response.messages.forEach((msg, index) => {
        const preview = msg.content.substring(0, 30) + (msg.content.length > 30 ? '...' : '')
        addTestResult('info', `${index + 1}. ${msg.role}: ${preview}`)
      })
      addTestResult('info', '====================')
    }

  } catch (error) {
    addTestResult('error', `API测试失败: ${error}`)
    if (error instanceof Error) {
      addTestResult('error', `错误详情: ${error.message}`)
    }
  }
}

// 模型测试
const selectModel = (event: Event) => {
  const target = event.target as HTMLSelectElement
  if (target.value) {
    const success = models.selectModel(target.value)
    const selectedModel = models.availableModels.value.find(m => m.id === target.value)

    addTestResult(
      success ? 'success' : 'error',
      `选择模型: ${success ? '成功' : '失败'} - ${selectedModel?.display_name || selectedModel?.name || target.value}`
    )

    if (success && selectedModel) {
      addTestResult('info', `模型详情:`)
      addTestResult('info', `  显示名: ${selectedModel.display_name || '无'}`)
      addTestResult('info', `  描述: ${selectedModel.description || '无描述'}`)
      addTestResult('info', `  能力: ${selectedModel.capabilities ? selectedModel.capabilities.join(', ') : '无'}`)
      addTestResult('info', `  提供商: ${selectedModel.provider || '未知'}`)
      addTestResult('info', `  状态: ${selectedModel.status || 'active'}`)
    }
  }
}

const testFetchModels = async () => {
  try {
    addTestResult('info', '开始获取模型列表...')
    await models.fetchModels()

    const modelCount = models.availableModels.value.length
    const activeCount = models.activeModels.value.length

    addTestResult('success', `获取模型列表成功: 总数=${modelCount}, 活跃=${activeCount}`)

    // 显示模型列表详情
    addTestResult('info', '=== 模型详细信息 ===')
    models.availableModels.value.forEach((model, index) => {
      addTestResult('info', `模型${index + 1}: ${model.display_name || model.name || model.id}`)
      addTestResult('info', `  描述: ${model.description || '无描述'}`)
      addTestResult('info', `  能力: ${model.capabilities ? model.capabilities.join(', ') : '无'}`)
      addTestResult('info', `  提供商: ${model.provider || '未知'}`)
      addTestResult('info', `  状态: ${model.status || 'active'}`)
      addTestResult('info', '---')
    })

    // 如果没有选中模型且有可用模型，自动选择第一个
    if (!models.selectedModelId.value && models.activeModels.value.length > 0) {
      const firstModel = models.activeModels.value[0]
      selectedModelForTest.value = firstModel.id
      models.selectModel(firstModel.id)
      addTestResult('info', `自动选择第一个活跃模型: ${firstModel.display_name || firstModel.name}`)
      if (firstModel.description) {
        addTestResult('info', `模型描述: ${firstModel.description}`)
      }
      if (firstModel.capabilities) {
        addTestResult('info', `模型能力: ${firstModel.capabilities.join(', ')}`)
      }
    }

  } catch (error) {
    addTestResult('error', `获取模型列表失败: ${error}`)
  }
}

const testModelPerformance = async () => {
  try {
    await models.fetchPerformance()
    addTestResult('success', '获取模型性能数据成功')
  } catch (error) {
    addTestResult('error', `获取性能数据失败: ${error}`)
  }
}

const testModelRecommendation = async () => {
  try {
    const recommendation = await models.getRecommendation('general')
    addTestResult('success', `推荐模型: ${recommendation?.name || '无'}`)
  } catch (error) {
    addTestResult('error', `获取推荐失败: ${error}`)
  }
}

const testRefreshModels = async () => {
  try {
    await models.refreshAll()
    addTestResult('success', '刷新模型数据成功')
  } catch (error) {
    addTestResult('error', `刷新模型数据失败: ${error}`)
  }
}

// 计费测试
const testFetchUsage = async () => {
  try {
    await billing.fetchUsage()
    addTestResult('success', '获取使用统计成功')
  } catch (error) {
    addTestResult('error', `获取使用统计失败: ${error}`)
  }
}

const testGenerateReport = async () => {
  try {
    const report = await billing.generateReport(
      new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      new Date().toISOString().split('T')[0]
    )
    addTestResult('success', `生成报告成功: ${report?.id}`)
  } catch (error) {
    addTestResult('error', `生成报告失败: ${error}`)
  }
}

const testMonitoring = async () => {
  try {
    await billing.runMonitoring()
    addTestResult('success', '运行监控检查成功')
  } catch (error) {
    addTestResult('error', `运行监控失败: ${error}`)
  }
}

const toggleAutoRefresh = () => {
  if (billing.autoRefreshEnabled) {
    billing.stopAutoRefresh()
    addTestResult('info', '停止自动刷新')
  } else {
    billing.startAutoRefresh()
    addTestResult('info', '启动自动刷新')
  }
}

// 通知测试
const testSuccessNotification = () => {
  notification.success('测试成功', '这是一个成功通知')
  addTestResult('success', '发送成功通知')
}

const testWarningNotification = () => {
  notification.warning('测试警告', '这是一个警告通知')
  addTestResult('success', '发送警告通知')
}

const testErrorNotification = () => {
  notification.error('测试错误', '这是一个错误通知')
  addTestResult('success', '发送错误通知')
}

const testConfirmNotification = () => {
  notification.confirm(
    '确认测试',
    '这是一个确认对话框',
    () => addTestResult('success', '用户确认'),
    () => addTestResult('info', '用户取消')
  )
  addTestResult('success', '发送确认通知')
}

// 权限测试
const testPermissionCheck = () => {
  const permissions = permission.getUserPermissions()
  addTestResult('success', `用户权限: ${permissions.join(', ')}`)
}

const testRoleCheck = () => {
  const role = auth.userRole.value
  const isAdmin = permission.hasRole('admin')
  addTestResult('success', `用户角色: ${role}, 管理员: ${isAdmin}`)
}

const testAdminAccess = () => {
  const canAccess = permission.requireAdmin({ silent: true })
  addTestResult(
    canAccess ? 'success' : 'error',
    `管理员访问: ${canAccess ? '允许' : '拒绝'}`
  )
}

const testPermissionGuard = () => {
  const canManageModels = permission.requirePermission('model:manage', { silent: true })
  addTestResult(
    canManageModels ? 'success' : 'error',
    `模型管理权限: ${canManageModels ? '有' : '无'}`
  )
}

// 表单验证测试
const initValidationForm = () => {
  validation.addFields({
    username: {
      value: '',
      rules: validation.rules.username()
    },
    email: {
      value: '',
      rules: [validation.rules.required(), validation.rules.email()]
    }
  })
  addTestResult('success', '初始化验证表单')
}

const testFormValidation = () => {
  const isValid = validation.validateForm()
  addTestResult(
    isValid ? 'success' : 'error',
    `表单验证: ${isValid ? '通过' : '失败'}`
  )
}

// WebSocket测试
const testWebSocketMessage = () => {
  const success = websocket.send({
    type: 'test_message',
    data: { content: '这是一个测试消息' },
    timestamp: Date.now()
  })
  addTestResult(
    success ? 'success' : 'error',
    `WebSocket消息: ${success ? '发送成功' : '发送失败'}`
  )
}

// 初始化
onMounted(async () => {
  addTestResult('info', 'Phase 5 组合式函数库测试页面已加载')

  // 初始化认证状态
  auth.initialize()

  // 初始化验证表单
  initValidationForm()

  // 显示初始状态
  addTestResult('info', `初始认证状态: ${auth.isAuthenticated.value ? '已登录' : '未登录'}`)

  // 自动获取模型列表
  if (auth.isAuthenticated.value) {
    addTestResult('info', '自动获取模型列表...')
    try {
      await testFetchModels()

      // 自动获取对话列表
      addTestResult('info', '自动获取对话列表...')
      await chat.refreshConversations()
      addTestResult('info', `对话列表已加载，共 ${conversationsLength.value} 个对话`)

      // 显示对话列表概览
      if (conversationsLength.value > 0) {
        addTestResult('info', '=== 对话列表概览 ===')
        chat.conversations.value?.slice(0, 5).forEach((conv, index) => {
          const preview = conv.last_message?.substring(0, 20) + (conv.last_message && conv.last_message.length > 20 ? '...' : '') || '暂无消息'
          addTestResult('info', `${index + 1}. ${conv.title || '未命名'}: ${preview}`)
        })
        if (conversationsLength.value > 5) {
          addTestResult('info', `... 还有 ${conversationsLength.value - 5} 个对话`)
        }
        addTestResult('info', '==================')
      }

      // 如果有当前对话，显示其消息数
      if (chat.currentConversation.value) {
        addTestResult('info', `当前对话: ${chat.currentConversation.value.title}`)
        addTestResult('info', `当前对话消息数: ${conversationMessages.value.length}`)
      }

    } catch (error) {
      addTestResult('error', `自动获取数据失败: ${error}`)
    }
  }
})
</script>

<style scoped>
.test-composables-view {
  padding: 20px;
  max-width: 1600px;
  margin: 0 auto;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  text-align: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.main-content {
  display: flex;
  gap: 20px;
  flex: 1;
  min-height: 0;
}

.header h1 {
  color: var(--primary-color, #3b82f6);
  margin-bottom: 10px;
}

.description {
  color: var(--text-secondary, #6b7280);
  font-size: 16px;
}

.test-sections {
  flex: 1;
  display: grid;
  gap: 20px;
  overflow-y: auto;
  padding-right: 10px;
}

.test-section {
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  padding: 20px;
  background: var(--bg-secondary, #ffffff);
}

.test-section h2 {
  margin: 0 0 15px 0;
  color: var(--text-primary, #1f2937);
  font-size: 18px;
}

.test-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.status-info {
  background: var(--bg-tertiary, #f9fafb);
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid var(--primary-color, #3b82f6);
}

.status-info p {
  margin: 5px 0;
  font-size: 14px;
}

.status-info strong {
  color: var(--text-primary, #1f2937);
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.model-selection {
  display: flex;
  gap: 10px;
  align-items: center;
  width: 100%;
  margin-bottom: 10px;
}

.model-selection .select {
  flex: 1;
  min-width: 200px;
}

.model-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 6px;
  background: var(--bg-primary, #ffffff);
  color: var(--text-primary, #374151);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn:hover {
  background: var(--bg-hover, #f3f4f6);
  border-color: var(--primary-color, #3b82f6);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn.success {
  background: #10b981;
  color: white;
  border-color: #10b981;
}

.btn.warning {
  background: #f59e0b;
  color: white;
  border-color: #f59e0b;
}

.btn.error {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
}

.input, .select {
  padding: 8px 12px;
  border: 1px solid var(--border-color, #d1d5db);
  border-radius: 6px;
  font-size: 14px;
  min-width: 200px;
}

.input:focus, .select:focus {
  outline: none;
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input.error {
  border-color: #ef4444;
}

.form-test {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.field label {
  font-weight: 500;
  color: var(--text-primary, #374151);
  font-size: 14px;
}

.error-text {
  color: #ef4444;
  font-size: 12px;
}

/* 对话列表显示样式 */
.conversation-list-display {
  margin-top: 20px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  background: var(--bg-tertiary, #f9fafb);
  max-height: 400px;
  display: flex;
  flex-direction: column;
}

.conversation-list-header {
  padding: 15px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background: var(--bg-secondary, #ffffff);
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conversation-list-header h4 {
  margin: 0;
  color: var(--text-primary, #1f2937);
  font-size: 16px;
}

.conversation-list-controls {
  display: flex;
  gap: 8px;
}

.conversation-list-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: var(--text-secondary, #6b7280);
}

.empty-state .text-secondary {
  font-size: 14px;
  margin-top: 8px;
}

.conversation-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.conversation-item {
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 6px;
  padding: 12px;
  background: var(--bg-secondary, #ffffff);
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.conversation-item:hover {
  border-color: var(--primary-color, #3b82f6);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.conversation-item.active {
  border-color: var(--primary-color, #3b82f6);
  background: #dbeafe;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.conversation-item.archived {
  opacity: 0.6;
  background: #f3f4f6;
}

.conversation-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.conversation-title {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-primary, #1f2937);
  flex: 1;
  margin-right: 10px;
}

.conversation-time {
  font-size: 11px;
  color: var(--text-secondary, #6b7280);
  white-space: nowrap;
}

.conversation-item-content {
  margin-bottom: 8px;
}

.last-message {
  margin: 0 0 6px 0;
  font-size: 13px;
  color: var(--text-secondary, #6b7280);
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.conversation-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 11px;
  color: var(--text-secondary, #6b7280);
}

.message-count {
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 4px;
}

.model-name {
  background: #e0f2fe;
  color: #0277bd;
  padding: 2px 6px;
  border-radius: 4px;
}

.tags {
  display: flex;
  gap: 4px;
}

.tag {
  background: #f0f9ff;
  color: #0369a1;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 10px;
}

.conversation-actions {
  display: flex;
  gap: 6px;
  position: absolute;
  top: 8px;
  right: 8px;
  opacity: 0;
  transition: opacity 0.2s;
}

.conversation-item:hover .conversation-actions {
  opacity: 1;
}

.btn-mini {
  padding: 2px 6px;
  font-size: 10px;
  border-radius: 3px;
}

.btn-mini.danger {
  background: #fee2e2;
  color: #dc2626;
  border-color: #fecaca;
}

.btn-mini.danger:hover {
  background: #fecaca;
  border-color: #f87171;
}

/* 对话历史显示样式 */
.conversation-display {
  margin-top: 20px;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  background: var(--bg-tertiary, #f9fafb);
  max-height: 500px;
  display: flex;
  flex-direction: column;
}

.conversation-header {
  padding: 15px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background: var(--bg-secondary, #ffffff);
  border-radius: 8px 8px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conversation-header h4 {
  margin: 0;
  color: var(--text-primary, #1f2937);
  font-size: 16px;
}

.conversation-info {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 12px;
  color: var(--text-secondary, #6b7280);
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-item {
  border-radius: 8px;
  padding: 12px;
  border-left: 4px solid;
  background: var(--bg-secondary, #ffffff);
}

.message-item.user {
  border-left-color: #3b82f6;
  background: #dbeafe;
}

.message-item.assistant {
  border-left-color: #10b981;
  background: #dcfce7;
}

.message-item.streaming {
  animation: pulse 2s infinite;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.message-role {
  font-weight: 600;
  color: var(--text-primary, #1f2937);
}

.message-time {
  color: var(--text-secondary, #6b7280);
  font-size: 11px;
}

.message-content {
  white-space: pre-wrap;
  line-height: 1.5;
  color: var(--text-primary, #1f2937);
  margin-bottom: 8px;
}

.message-status {
  font-size: 11px;
  color: var(--text-secondary, #6b7280);
}

.thinking-content {
  margin: 8px 0;
  padding: 10px;
  background: #fef3c7;
  border-radius: 6px;
  border-left: 4px solid #f59e0b;
}

.thinking-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 12px;
}

.thinking-icon {
  font-size: 14px;
}

.thinking-indicator {
  color: #f59e0b;
  font-size: 11px;
  animation: pulse 1.5s infinite;
}

.thinking-text {
  white-space: pre-wrap;
  font-family: monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #92400e;
}

.complete-indicator {
  color: #10b981;
  font-size: 11px;
  font-weight: 500;
}

.streaming-indicator {
  color: #3b82f6;
  font-size: 11px;
  font-weight: 500;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.test-results {
  width: 400px;
  flex-shrink: 0;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 8px;
  padding: 20px;
  background: var(--bg-secondary, #ffffff);
  display: flex;
  flex-direction: column;
  max-height: 100%;
}

.results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.results-header h2 {
  margin: 0;
  color: var(--text-primary, #1f2937);
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

.results-content {
  flex: 1;
  overflow-y: auto;
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 6px;
  background: var(--bg-tertiary, #f9fafb);
  min-height: 0;
}

.result-item {
  padding: 10px 15px;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  display: flex;
  gap: 10px;
  font-size: 14px;
}

.result-item:last-child {
  border-bottom: none;
}

.result-item.success {
  background: rgba(16, 185, 129, 0.1);
  border-left: 4px solid #10b981;
}

.result-item.error {
  background: rgba(239, 68, 68, 0.1);
  border-left: 4px solid #ef4444;
}

.result-item.info {
  background: rgba(59, 130, 246, 0.1);
  border-left: 4px solid #3b82f6;
}

.timestamp {
  color: var(--text-secondary, #6b7280);
  font-size: 12px;
  white-space: nowrap;
}

.message {
  flex: 1;
}

.connected {
  color: #10b981;
  font-weight: 500;
}

.connecting {
  color: #f59e0b;
  font-weight: 500;
}

.disconnected {
  color: #ef4444;
  font-weight: 500;
}

.success {
  color: #10b981;
}

.warning {
  color: #f59e0b;
}

.error {
  color: #ef4444;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    flex-direction: column;
  }

  .test-results {
    width: 100%;
    max-height: 300px;
  }
}

@media (max-width: 768px) {
  .test-composables-view {
    padding: 15px;
    height: auto;
  }

  .main-content {
    flex-direction: column;
  }

  .test-results {
    width: 100%;
    max-height: 250px;
  }

  .actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
  }

  .input, .select {
    min-width: auto;
    width: 100%;
  }
}

/* 深色主题支持 */
.dark .test-section {
  background: var(--bg-secondary-dark, #1f2937);
  border-color: var(--border-color-dark, #374151);
}

.dark .status-info {
  background: var(--bg-tertiary-dark, #111827);
}

.dark .btn {
  background: var(--bg-primary-dark, #374151);
  color: var(--text-primary-dark, #f9fafb);
  border-color: var(--border-color-dark, #4b5563);
}

.dark .btn:hover {
  background: var(--bg-hover-dark, #4b5563);
}

.dark .input, .dark .select {
  background: var(--bg-primary-dark, #374151);
  color: var(--text-primary-dark, #f9fafb);
  border-color: var(--border-color-dark, #4b5563);
}

.dark .test-results {
  background: var(--bg-secondary-dark, #1f2937);
  border-color: var(--border-color-dark, #374151);
}

.dark .results-content {
  background: var(--bg-tertiary-dark, #111827);
  border-color: var(--border-color-dark, #374151);
}

.dark .result-item {
  border-color: var(--border-color-dark, #374151);
}
</style>
