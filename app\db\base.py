# Import all the models, so that Base has them before being
# imported by <PERSON><PERSON><PERSON>
from app.db.base_class import Base
from app.models.user import User
from app.models.role import Role
from app.models.conversation import Conversation
from app.models.message import Message
from app.models.feedback import Feedback
from app.models.product import Product

from app.models.model_configuration import ModelConfiguration
from app.models.model_price_history import ModelPriceHistory
from app.models.model_usage_daily import ModelUsageDaily
from app.models.model_usage_hourly import ModelUsageHourly
from app.models.model_usage_log import ModelUsageLog
from app.models.model_performance_test import ModelPerformanceTest
from app.models.model_audit_log import ModelAuditLog
from app.models.user_model_access import UserModelAccess
from app.models.user_usage_stats import UserUsageStats