<template>
  <div class="debug-auth-view">
    <el-container>
      <el-header>
        <h1>认证调试页面</h1>
      </el-header>
      
      <el-main>
        <el-card header="当前认证状态">
          <div class="debug-info">
            <h3>Store状态:</h3>
            <pre>{{ JSON.stringify(authStoreState, null, 2) }}</pre>
            
            <h3>本地存储:</h3>
            <pre>{{ JSON.stringify(localStorageState, null, 2) }}</pre>
            
            <h3>计算属性:</h3>
            <pre>{{ JSON.stringify(computedState, null, 2) }}</pre>
          </div>
          
          <el-divider />
          
          <el-button-group>
            <el-button @click="refreshState" type="primary">刷新状态</el-button>
            <el-button @click="testLogin" :loading="authStore.isLoading" type="success">
              测试登录
            </el-button>
            <el-button @click="testGetCurrentUser" :loading="isGettingUser">
              获取用户信息
            </el-button>
            <el-button @click="clearStorage" type="danger">清除存储</el-button>
          </el-button-group>
          
          <el-divider />
          
          <div v-if="logs.length > 0">
            <h3>操作日志:</h3>
            <div class="logs">
              <div v-for="(log, index) in logs" :key="index" class="log-item">
                <span class="log-time">{{ log.time }}</span>
                <span :class="['log-level', `log-${log.level}`]">{{ log.level.toUpperCase() }}</span>
                <span class="log-message">{{ log.message }}</span>
                <pre v-if="log.data" class="log-data">{{ JSON.stringify(log.data, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </el-card>
      </el-main>
    </el-container>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useAuthStore } from '@/stores'
import { authService } from '@/services'

const authStore = useAuthStore()
const isGettingUser = ref(false)
const logs = ref<Array<{
  time: string
  level: 'info' | 'error' | 'success'
  message: string
  data?: any
}>>([])

// 状态计算
const authStoreState = computed(() => ({
  user: authStore.user,
  token: authStore.token ? `${authStore.token.substring(0, 20)}...` : null,
  isAuthenticated: authStore.isAuthenticated,
  isLoading: authStore.isLoading,
  lastError: authStore.lastError,
  lastLoginTime: authStore.lastLoginTime
}))

const localStorageState = computed(() => ({
  auth_token: localStorage.getItem('auth_token'),
  user_info: localStorage.getItem('user_info'),
  refresh_token: localStorage.getItem('refresh_token'),
  lastLoginTime: localStorage.getItem('lastLoginTime')
}))

const computedState = computed(() => ({
  isAuthenticated: authStore.isAuthenticated,
  userRole: authStore.userRole,
  isAdmin: authStore.isAdmin,
  userName: authStore.userName,
  userEmail: authStore.userEmail
}))

// 日志函数
const addLog = (level: 'info' | 'error' | 'success', message: string, data?: any) => {
  logs.value.unshift({
    time: new Date().toLocaleTimeString(),
    level,
    message,
    data
  })
  
  // 限制日志数量
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

// 刷新状态
const refreshState = () => {
  addLog('info', '刷新状态')
  authStore.initializeAuth()
}

// 测试登录
const testLogin = async () => {
  try {
    addLog('info', '开始登录测试')
    
    const success = await authStore.login({
      username: 'admin',
      password: 'admin123'
    })
    
    addLog(success ? 'success' : 'error', `登录${success ? '成功' : '失败'}`, {
      success,
      user: authStore.user,
      token: authStore.token ? `${authStore.token.substring(0, 20)}...` : null,
      isAuthenticated: authStore.isAuthenticated
    })
    
  } catch (error) {
    addLog('error', '登录异常', error)
  }
}

// 测试获取用户信息
const testGetCurrentUser = async () => {
  try {
    isGettingUser.value = true
    addLog('info', '开始获取用户信息')
    
    const userInfo = await authService.getCurrentUser()
    addLog('success', '获取用户信息成功', userInfo)
    
  } catch (error) {
    addLog('error', '获取用户信息失败', error)
  } finally {
    isGettingUser.value = false
  }
}

// 清除存储
const clearStorage = () => {
  localStorage.removeItem('auth_token')
  localStorage.removeItem('user_info')
  localStorage.removeItem('refresh_token')
  localStorage.removeItem('lastLoginTime')
  authStore.clearAuthState()
  addLog('info', '已清除所有存储')
}

// 页面加载时初始化
onMounted(() => {
  addLog('info', '页面加载，初始化认证状态')
  authStore.initializeAuth()
})
</script>

<style scoped>
.debug-auth-view {
  padding: 20px;
}

.debug-info {
  margin-bottom: 20px;
}

.debug-info h3 {
  margin: 15px 0 10px 0;
  color: #409eff;
}

.debug-info pre {
  background: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
}

.logs {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px;
}

.log-item {
  margin-bottom: 10px;
  padding: 8px;
  border-radius: 4px;
  background: #f9f9f9;
}

.log-time {
  color: #666;
  font-size: 12px;
  margin-right: 10px;
}

.log-level {
  font-weight: bold;
  margin-right: 10px;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
}

.log-info {
  background: #e1f5fe;
  color: #0277bd;
}

.log-success {
  background: #e8f5e8;
  color: #2e7d32;
}

.log-error {
  background: #ffebee;
  color: #c62828;
}

.log-message {
  font-size: 14px;
}

.log-data {
  margin-top: 5px;
  background: #fff;
  padding: 8px;
  border-radius: 3px;
  font-size: 11px;
  max-height: 150px;
  overflow-y: auto;
}
</style>
