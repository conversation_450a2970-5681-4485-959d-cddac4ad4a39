<template>
  <div class="auth-debug-view">
    <div class="debug-header">
      <h1>认证状态调试页面</h1>
      <p>用于诊断登录和路由跳转问题</p>
    </div>

    <div class="debug-sections">
      <!-- 认证状态 -->
      <div class="debug-section">
        <h2>🔐 认证状态</h2>
        <div class="debug-content">
          <div class="status-grid">
            <div class="status-item">
              <label>认证状态:</label>
              <span :class="authStore.isAuthenticated ? 'success' : 'error'">
                {{ authStore.isAuthenticated ? '已认证' : '未认证' }}
              </span>
            </div>
            <div class="status-item">
              <label>用户对象:</label>
              <span :class="authStore.user ? 'success' : 'error'">
                {{ authStore.user ? '存在' : '不存在' }}
              </span>
            </div>
            <div class="status-item">
              <label>Token:</label>
              <span :class="authStore.token ? 'success' : 'error'">
                {{ authStore.token ? '存在' : '不存在' }}
              </span>
            </div>
            <div class="status-item">
              <label>用户名:</label>
              <span>{{ authStore.user?.username || '无' }}</span>
            </div>
            <div class="status-item">
              <label>用户角色:</label>
              <span>{{ authStore.userRole || '无' }}</span>
            </div>
            <div class="status-item">
              <label>最后登录:</label>
              <span>{{ authStore.lastLoginTime || '无' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 本地存储 -->
      <div class="debug-section">
        <h2>💾 本地存储</h2>
        <div class="debug-content">
          <div class="storage-grid">
            <div class="storage-item">
              <label>auth-store (Pinia持久化):</label>
              <pre>{{ getStorageItem('ai-chat-auth-store') }}</pre>
            </div>
            <div class="storage-item">
              <label>auth_token (旧版):</label>
              <pre>{{ getStorageItem('auth_token') }}</pre>
            </div>
            <div class="storage-item">
              <label>user_info (旧版):</label>
              <pre>{{ getStorageItem('user_info') }}</pre>
            </div>
            <div class="storage-item">
              <label>login_debug_log:</label>
              <pre>{{ getStorageItem('login_debug_log') }}</pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 路由信息 -->
      <div class="debug-section">
        <h2>🛣️ 路由信息</h2>
        <div class="debug-content">
          <div class="route-grid">
            <div class="route-item">
              <label>当前路径:</label>
              <span>{{ route.path }}</span>
            </div>
            <div class="route-item">
              <label>完整路径:</label>
              <span>{{ route.fullPath }}</span>
            </div>
            <div class="route-item">
              <label>查询参数:</label>
              <pre>{{ JSON.stringify(route.query, null, 2) }}</pre>
            </div>
            <div class="route-item">
              <label>路由元数据:</label>
              <pre>{{ JSON.stringify(route.meta, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="debug-section">
        <h2>🔧 调试操作</h2>
        <div class="debug-content">
          <div class="actions">
            <button @click="refreshAuthState" class="btn btn-primary">
              刷新认证状态
            </button>
            <button @click="clearAuthState" class="btn btn-warning">
              清除认证状态
            </button>
            <button @click="testLogin" class="btn btn-success">
              测试登录
            </button>
            <button @click="goToLogin" class="btn btn-secondary">
              前往登录页面
            </button>
            <button @click="goToChat" class="btn btn-secondary">
              前往聊天页面
            </button>
            <button @click="goToTestComposables" class="btn btn-secondary">
              前往测试页面
            </button>
          </div>
        </div>
      </div>

      <!-- 调试日志 -->
      <div class="debug-section">
        <h2>📝 调试日志</h2>
        <div class="debug-content">
          <div class="log-container">
            <div v-for="log in debugLogs" :key="log.id" class="log-item" :class="log.type">
              <span class="log-time">{{ formatTime(log.timestamp) }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
          <button @click="clearLogs" class="btn btn-small">清除日志</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()

const debugLogs = ref<Array<{
  id: string
  type: 'info' | 'success' | 'error' | 'warning'
  message: string
  timestamp: Date
}>>([])

let logCounter = 0

const addLog = (type: 'info' | 'success' | 'error' | 'warning', message: string) => {
  logCounter++
  debugLogs.value.unshift({
    id: `log-${logCounter}-${Date.now()}`,
    type,
    message,
    timestamp: new Date()
  })

  // 限制日志数量
  if (debugLogs.value.length > 50) {
    debugLogs.value = debugLogs.value.slice(0, 50)
  }
}

const clearLogs = () => {
  debugLogs.value = []
  addLog('info', '调试日志已清除')
}

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString()
}

const getStorageItem = (key: string): string => {
  try {
    const item = localStorage.getItem(key)
    if (!item) return '(不存在)'
    
    // 尝试解析JSON
    try {
      const parsed = JSON.parse(item)
      return JSON.stringify(parsed, null, 2)
    } catch {
      return item
    }
  } catch (error) {
    return `(错误: ${error})`
  }
}

const refreshAuthState = () => {
  addLog('info', '刷新认证状态...')
  authStore.initializeAuth()
  addLog('success', `认证状态已刷新: ${authStore.isAuthenticated}`)
}

const clearAuthState = () => {
  addLog('warning', '清除认证状态...')
  authStore.clearAuthState()
  addLog('success', '认证状态已清除')
}

const testLogin = async () => {
  try {
    addLog('info', '测试登录...')
    const success = await authStore.login({
      username: 'admin',
      password: 'admin123'
    })
    
    if (success) {
      addLog('success', '测试登录成功')
    } else {
      addLog('error', '测试登录失败')
    }
  } catch (error) {
    addLog('error', `测试登录异常: ${error}`)
  }
}

const goToLogin = () => {
  addLog('info', '跳转到登录页面')
  router.push('/login')
}

const goToChat = () => {
  addLog('info', '跳转到聊天页面')
  router.push('/chat')
}

const goToTestComposables = () => {
  addLog('info', '跳转到测试页面')
  router.push('/test-composables')
}

onMounted(() => {
  addLog('info', '认证调试页面已加载')
  addLog('info', `当前认证状态: ${authStore.isAuthenticated}`)
  addLog('info', `当前路径: ${route.fullPath}`)
})
</script>

<style scoped>
.auth-debug-view {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
  background: #f8fafc;
  min-height: 100vh;
}

.debug-header {
  text-align: center;
  margin-bottom: 2rem;
}

.debug-header h1 {
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.debug-header p {
  color: #718096;
}

.debug-sections {
  display: grid;
  gap: 1.5rem;
}

.debug-section {
  background: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.debug-section h2 {
  margin: 0 0 1rem 0;
  color: #2d3748;
  font-size: 1.25rem;
}

.status-grid,
.storage-grid,
.route-grid {
  display: grid;
  gap: 1rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.status-item,
.storage-item,
.route-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.status-item label,
.storage-item label,
.route-item label {
  font-weight: 600;
  color: #4a5568;
  font-size: 0.875rem;
}

.status-item span {
  padding: 0.5rem;
  border-radius: 0.25rem;
  font-family: monospace;
}

.success {
  background: #f0fff4;
  color: #22543d;
  border: 1px solid #9ae6b4;
}

.error {
  background: #fff5f5;
  color: #742a2a;
  border: 1px solid #feb2b2;
}

.storage-item pre,
.route-item pre {
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.25rem;
  padding: 0.75rem;
  font-size: 0.75rem;
  overflow-x: auto;
  max-height: 200px;
  overflow-y: auto;
}

.actions {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s;
}

.btn-primary {
  background: #3182ce;
  color: white;
}

.btn-primary:hover {
  background: #2c5282;
}

.btn-secondary {
  background: #718096;
  color: white;
}

.btn-secondary:hover {
  background: #4a5568;
}

.btn-success {
  background: #38a169;
  color: white;
}

.btn-success:hover {
  background: #2f855a;
}

.btn-warning {
  background: #ed8936;
  color: white;
}

.btn-warning:hover {
  background: #dd6b20;
}

.btn-small {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  padding: 0.5rem;
  background: #f7fafc;
  margin-bottom: 1rem;
}

.log-item {
  display: flex;
  gap: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
}

.log-item.info {
  background: #ebf8ff;
  color: #2a4365;
}

.log-item.success {
  background: #f0fff4;
  color: #22543d;
}

.log-item.error {
  background: #fff5f5;
  color: #742a2a;
}

.log-item.warning {
  background: #fffbf0;
  color: #744210;
}

.log-time {
  color: #718096;
  white-space: nowrap;
  font-family: monospace;
  font-size: 0.75rem;
}

.log-message {
  flex: 1;
}
</style>
