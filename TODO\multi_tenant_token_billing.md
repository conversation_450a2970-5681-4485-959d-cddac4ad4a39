# 多租户Token计费系统设计与实现计划

## 📋 概述

本文档记录了Token计费系统多租户功能的设计讨论、技术方案和实施计划。目标是为不同租户提供独立的模型访问控制和Token使用限制。

## 🔍 当前系统现状分析

### ✅ 已有的多租户基础

#### 1. 用户权限系统
- **User模型**：包含 `groups` 字段（逗号分隔的角色列表）
- **Role模型**：角色定义和用户角色关联
- **RBAC权限控制**：基于角色的访问控制机制
- **权限检查依赖**：`has_required_role()`, `require_roles()` 等

#### 2. 模型访问控制基础
- **UserModelAccess模型**：已定义用户-模型访问权限表
  ```sql
  - user_id: 用户ID
  - model_id: 模型ID  
  - access_level: 访问级别 (read/write/admin)
  - has_access: 是否有访问权限
  - daily_quota: 每日请求限制
  - token_quota: 每日token限制
  - custom_settings: 用户自定义模型设置
  ```
- **Schema定义**：`UserAccessCreate`, `UserAccessUpdate`, `UserAccessResponse`

#### 3. 配置系统多作用域支持
- **ConfigScope枚举**：已包含 `GLOBAL`, `USER`, `MODEL`, `TENANT` 作用域
- **ConfigurationManager**：支持不同作用域的配置管理
- **动态配置**：支持实时配置更新和监听

### ❌ 缺失的多租户功能

#### 1. 租户实体和层级
- **缺少Tenant/Organization模型**：没有明确的租户概念
- **缺少Team/Department模型**：没有中间层级组织结构
- **缺少租户层级关系**：用户与租户的归属关系

#### 2. 服务层实现
- **user_model_access_service.py为空**：核心服务未实现
- **租户管理服务**：租户CRUD和层级管理
- **多租户计费服务**：租户隔离的使用统计

#### 3. Token限制执行
- **实时限制检查**：Token使用前的配额验证
- **限制策略执行**：硬限制vs软限制的实现
- **配额监控告警**：超限预警和通知

#### 4. 数据隔离和统计
- **租户级使用统计**：独立的使用数据统计
- **跨租户数据隔离**：确保租户间数据安全
- **分层统计报告**：支持租户内和跨租户统计

## 🎯 多租户架构设计方案

### 1. 租户层级设计选项

#### 选项A：简单两层结构
```
Organization (组织)
  └── User (用户)
```
**优点**：简单直接，易于实现和维护
**缺点**：缺乏中间层级，不够灵活

#### 选项B：三层结构 ⭐ 推荐
```
Organization (组织)
  └── Team (团队/部门)
    └── User (用户)
```
**优点**：平衡了复杂性和灵活性，适合大多数企业场景
**缺点**：比简单结构复杂一些

#### 选项C：灵活多层级
```
Organization (组织)
  └── Department (部门)
    └── Team (团队)
      └── User (用户)
```
**优点**：最大灵活性，支持复杂组织结构
**缺点**：实现复杂，可能过度设计

### 2. Token限制策略设计

#### 策略A：硬限制
- **机制**：达到限制立即拒绝请求
- **优点**：严格控制成本，逻辑简单
- **缺点**：用户体验可能不佳，缺乏灵活性

#### 策略B：软限制 + 告警 ⭐ 推荐
- **机制**：超限后继续服务但发送告警
- **优点**：用户体验好，有预警机制
- **缺点**：需要额外的告警系统

#### 策略C：分级限制
- **机制**：基础配额 + 突发配额 + 紧急配额
- **优点**：最大灵活性，支持业务突发需求
- **缺点**：实现复杂，管理成本高

### 3. 权限控制粒度

#### 当前支持
- **用户 → 模型**：基本的用户-模型访问控制
- **访问级别**：read/write/admin三级权限
- **配额限制**：daily_quota, token_quota

#### 扩展维度
- **租户 → 模型类型**：租户级别的模型类型访问控制
- **时间维度**：工作时间限制、有效期控制
- **地理维度**：基于地理位置的访问控制
- **成本维度**：基于成本的动态限制

### 4. 数据隔离策略

#### 完全隔离模式
- **租户数据完全分离**：每个租户独立的使用统计
- **优点**：最高安全性，符合合规要求
- **缺点**：无法进行跨租户分析

#### 分层统计模式 ⭐ 推荐
- **租户内统计 + 全局统计**：支持多层级数据视图
- **权限控制访问**：管理员可查看全局，租户管理员只能查看本租户
- **优点**：平衡了安全性和分析需求

## 🏗️ 技术实现方案

### 1. 数据库模型设计

#### 租户相关模型
```python
# 组织模型
class Organization(Base):
    id: UUID
    name: str
    description: str
    settings: JSONB  # 组织级配置
    token_budget: Decimal  # 组织Token预算
    created_at: datetime
    updated_at: datetime

# 团队模型  
class Team(Base):
    id: UUID
    organization_id: UUID  # 外键
    name: str
    description: str
    token_budget: Decimal  # 团队Token预算
    created_at: datetime
    updated_at: datetime

# 用户-组织关联
class UserOrganization(Base):
    user_id: UUID
    organization_id: UUID
    team_id: UUID
    role: str  # 在组织中的角色
    joined_at: datetime
```

#### 多租户使用统计模型
```python
# 租户级使用统计
class TenantUsageDaily(Base):
    organization_id: UUID
    team_id: UUID
    usage_date: date
    total_requests: int
    total_tokens: int
    total_cost: Decimal
    # ... 其他统计字段

# 租户模型访问统计
class TenantModelUsage(Base):
    organization_id: UUID
    team_id: UUID
    model_id: UUID
    usage_date: date
    # ... 统计字段
```

### 2. 服务层架构

#### 核心服务
```python
# 租户管理服务
class TenantManagementService:
    async def create_organization()
    async def create_team()
    async def assign_user_to_team()
    async def get_user_tenant_context()

# 用户模型访问服务
class UserModelAccessService:
    async def check_model_access()
    async def check_token_quota()
    async def record_usage()
    async def get_remaining_quota()

# 多租户计费服务
class MultiTenantBillingService:
    async def calculate_tenant_cost()
    async def generate_tenant_report()
    async def check_budget_limits()
```

#### Token使用拦截器增强
```python
class MultiTenantTokenInterceptor(TokenUsageInterceptor):
    async def pre_request_check():
        # 1. 获取用户租户上下文
        # 2. 检查租户级配额
        # 3. 检查用户级配额
        # 4. 检查模型访问权限
        
    async def post_request_record():
        # 1. 记录租户级使用
        # 2. 记录用户级使用
        # 3. 更新配额计数
        # 4. 检查告警阈值
```

### 3. API设计

#### 租户管理API
```
POST /api/v1/tenants/organizations
GET  /api/v1/tenants/organizations
PUT  /api/v1/tenants/organizations/{org_id}

POST /api/v1/tenants/teams
GET  /api/v1/tenants/teams
PUT  /api/v1/tenants/teams/{team_id}

POST /api/v1/tenants/users/{user_id}/assign
GET  /api/v1/tenants/users/{user_id}/context
```

#### 多租户计费API
```
GET  /api/v1/tenant-billing/usage
GET  /api/v1/tenant-billing/quotas
PUT  /api/v1/tenant-billing/quotas
GET  /api/v1/tenant-billing/reports
```

## 📅 实施计划

### Phase 1: 基础租户模型 (1-2周)
- [ ] 创建Organization, Team, UserOrganization模型
- [ ] 实现基础的租户管理服务
- [ ] 创建租户管理API端点
- [ ] 数据库迁移脚本

### Phase 2: 用户模型访问服务 (1-2周)
- [ ] 实现UserModelAccessService完整功能
- [ ] 集成租户上下文到权限检查
- [ ] 实现配额检查和限制逻辑
- [ ] 单元测试和集成测试

### Phase 3: Token使用拦截增强 (1周)
- [ ] 扩展TokenUsageInterceptor支持多租户
- [ ] 实现实时配额检查
- [ ] 集成到现有LLM调用流程
- [ ] 性能优化和缓存

### Phase 4: 多租户计费统计 (1-2周)
- [ ] 创建租户级使用统计模型
- [ ] 实现多租户计费服务
- [ ] 数据隔离和安全控制
- [ ] 报告生成和导出

### Phase 5: 管理界面和监控 (1-2周)
- [ ] 租户管理界面
- [ ] 配额管理界面
- [ ] 使用统计仪表板
- [ ] 告警和通知系统

### Phase 6: 测试和优化 (1周)
- [ ] 端到端测试
- [ ] 性能测试和优化
- [ ] 安全测试
- [ ] 文档完善

## 🤔 待讨论的关键决策

### 1. 租户层级选择
**问题**：选择哪种租户层级结构？
**选项**：简单两层 vs 三层结构 vs 灵活多层级
**影响**：系统复杂度、扩展性、用户体验

### 2. Token限制策略
**问题**：采用哪种Token限制策略？
**选项**：硬限制 vs 软限制+告警 vs 分级限制
**影响**：用户体验、成本控制、系统复杂度

### 3. 数据隔离程度
**问题**：租户间数据隔离到什么程度？
**选项**：完全隔离 vs 分层统计
**影响**：安全性、分析能力、合规要求

### 4. 权限控制粒度
**问题**：需要哪些维度的权限控制？
**选项**：基础用户-模型 vs 扩展多维度控制
**影响**：功能丰富度、实现复杂度

### 5. 现有数据迁移
**问题**：如何处理现有数据和向后兼容？
**选项**：强制迁移 vs 渐进式迁移 vs 双模式运行
**影响**：部署复杂度、用户影响、开发工作量

## 📚 相关资源

### 现有代码文件
- `app/models/user_model_access.py` - 用户模型访问权限模型
- `app/schemas/model_management/access.py` - 访问权限Schema
- `app/services/model_management/user_model_access_service.py` - 待实现的服务
- `app/services/token_billing/` - Token计费系统核心模块

### 参考文档
- [Token计费系统架构文档](../docs/token_billing_architecture.md)
- [权限系统设计文档](../docs/permission_system.md)
- [多租户最佳实践](../docs/multi_tenant_best_practices.md)

## 📝 更新日志

- **2024-12-19**: 初始文档创建，记录多租户功能设计讨论
- **待更新**: 根据进一步讨论更新设计方案和实施计划

---

**下一步行动**: 等待产品需求确认，然后开始Phase 1的实施工作。
