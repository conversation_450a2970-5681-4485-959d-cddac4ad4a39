/**
 * 测试模型API的工具函数
 */

import { modelsAPI } from '@/services/api/models'
import { ModelStatus } from '@/types'

export async function testModelsAPI() {
  console.log('🧪 开始测试模型API...')
  
  try {
    // 测试获取模型列表
    console.log('📋 测试获取模型列表...')
    const models = await modelsAPI.getAvailableModels()
    
    console.log(`✅ 成功获取 ${models.length} 个模型`)
    
    // 显示模型详情
    models.forEach((model, index) => {
      console.log(`${index + 1}. ${model.display_name || model.name}`)
      console.log(`   ID: ${model.id}`)
      console.log(`   类型: ${model.model_type}`)
      console.log(`   状态: ${model.status}`)
      console.log(`   能力: ${model.capabilities?.join(', ') || '无'}`)
      console.log(`   描述: ${model.description}`)
      console.log(`   活跃: ${model.is_active}`)
      console.log('   ---')
    })
    
    // 测试 ModelStatus 枚举
    console.log('🔍 测试 ModelStatus 枚举:')
    console.log(`   ACTIVE: ${ModelStatus.ACTIVE}`)
    console.log(`   INACTIVE: ${ModelStatus.INACTIVE}`)
    console.log(`   MAINTENANCE: ${ModelStatus.MAINTENANCE}`)
    console.log(`   DEPRECATED: ${ModelStatus.DEPRECATED}`)
    
    return {
      success: true,
      models,
      count: models.length
    }
    
  } catch (error) {
    console.error('❌ 模型API测试失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : String(error)
    }
  }
}

// 在浏览器控制台中可以调用的测试函数
if (typeof window !== 'undefined') {
  (window as any).testModelsAPI = testModelsAPI
}
