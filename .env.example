# API Settings
API_V1_STR=/api/v1
PROJECT_NAME="Azure Calculator backend API"
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8000"]

# 标准OpenAI API配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_BASE=  # 可选，为空则使用官方API端点
OPENAI_CHAT_MODEL=gpt-3.5-turbo# 或其他模型如 gpt-4

# Deepseek API配置
DEEPSEEK_API_BASE=https://api.deepseek.com
DEEPSEEK_API_KEY=your_api_key_here
DEEPSEEK_V3_MODEL=deepseek-chat
DEEPSEEK_R1_MODEL=deepseek-reasoner

# Azure OpenAI配置（保留但不使用）
AZURE_OPENAI_API_KEY=your_azure_api_key_here
AZURE_OPENAI_API_VERSION=2023-05-15
AZURE_OPENAI_API_BASE=https://your-resource-name.openai.azure.com
AZURE_OPENAI_DEPLOYMENT_NAME=your_deployment_name

# 意图分析专用模型配置
INTENT_ANALYSIS_MODEL_TYPE=deepseek
INTENT_ANALYSIS_MODEL=deepseek-chat
INTENT_ANALYSIS_TEMPERATURE=0.3
INTENT_ANALYSIS_MAX_TOKENS=500
INTENT_CACHE_TTL=50000
INTENT_CACHE_ENABLED=True
INTENT_SIMILARITY_THRESHOLD=0.3

DEFAULT_MODEL_TYPE=deepseek

# Security Settings
SECRET_KEY=YourSuperSecretKeyHere123!@#$%^&*()
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

API_KEY_ENCRYPTION_KEY=your_secure_fernet_key_here

# PostgreSQL settings (development)
POSTGRES_SERVER=localhost
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=azure_calculator

# Celery settings
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# ========================
# LDAP 配置
# ========================
LDAP_ENABLED=false
LDAP_SERVER=ldap://ad01.example.com
LDAP_PORT=389
LDAP_DOMAIN="example.com"
LDAP_BASE_DN="DC=example,DC=com"
LDAP_BIND_DN="CN=admin,CN=Users,DC=example,DC=com"
LDAP_BIND_PASSWORD="your_secure_password_here"
LDAP_GROUP_MAPPINGS={"Domain Admins": "admin", "IT Department": "it_admin"}

# Logging settings
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_ROTATION=500 MB
LOG_RETENTION=10 days

# RAG Configuration
RAG_MODE=hybrid
RAG_RETRIEVER_TYPE=vector
RAG_RETRIEVER_TOP_K=5
RAG_RETRIEVER_SCORE_THRESHOLD=0.7
RAG_VECTOR_STORE_TYPE=memory

# LlamaIndex Configuration
LLAMA_INDEX_EMBED_MODEL=text-embedding-ada-002
LLAMA_INDEX_LLM_MODEL=gpt-3.5-turbo
LLAMA_INDEX_CHUNK_SIZE=1000
LLAMA_INDEX_CHUNK_OVERLAP=200
LLAMA_INDEX_RESPONSE_MODE=compact